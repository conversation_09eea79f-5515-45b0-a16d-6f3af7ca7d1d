{"templateCommon.tempMgmt": "Template management", "templateCommon.usageHelp": "Usage Guide", "templateCommon.mark": "Remarks", "templateCommon.set": "Setting", "templateCommon.operation": "Operation", "templateCommon.selectedFile": "{number} documents selected", "templateCommon.edit": "Edit", "templateCommon.approval": "Approval", "templateCommon.add": "Add", "templateCommon.use": "Use", "templateCommon.delete": "Delete", "templateCommon.deleteSucc": "Delete success", "templateCommon.batchDelete": "Bulk delete", "templateCommon.copy": "Copy", "templateCommon.group": "Group", "templateCommon.remove": "Remove", "templateCommon.confirm": "OK", "templateCommon.singleSendContract": "Single Send contract", "templateCommon.batchSendContract": "Bulk send contracts with Excel", "templateCommon.cancel": "Cancel", "templateCommon.save": "Save", "templateCommon.inputPlaceHolder": "Please enter", "templateCommon.selectPlaceHolder": "Please select", "templateCommon.reset": "Reset", "templateCommon.search": "Search", "templateCommon.searchPlaceholder": "Please enter your search content", "templateCommon.btnCreateTemp": "Create a template now", "templateCommon.next": "Next", "templateCommon.previous": "Back", "templateCommon.rename": "<PERSON><PERSON>", "templateCommon.stick": "Top", "templateCommon.tip": "Tip", "templateCommon.complete": "Confirm", "templateCommon.quit": "Quit", "templateCommon.copySucc": "Copy success", "templateCommon.copyFail": "Co<PERSON> failed", "templateCommon.setSuccess": "Successful setup", "templateCommon.moneyUnit.RMB": "人民币", "templateCommon.moneyUnit.EUR": "欧元", "templateCommon.moneyUnit.Dollar": "美元", "templateCommon.moneyUnit.GBP": "英镑", "templateCommon.userGuide": "Shortcut settings guide", "templateCommon.understand": "Got it", "templateCommon.setting": "More settings", "templateCommon.selectAll": "Select all", "templateCommon.allType": "No format restrictions", "templateCommon.warmTip": "Tip", "templateCommon.useTip": "Tips", "templateCommon.goOn": "Continue", "templateCommon.abandon": "Cancel", "templateCommon.tempSave": "temporarily save and exit", "templateCommon.specialSealConfirm": "The following signatories have not configured a template specific seal and can use <b>any seal</b> to sign:", "templateDetail.basic.docComId": "Document combination ID:", "templateDetail.basic.docComCreater": "Document combination created by:", "templateDetail.basic.docComCreateTime": "Document combination creation date:", "templateDetail.basic.docComRemark": "Document combination remarks:", "templateDetail.basic.batchSendContract": "Bulk send contracts", "templateDetail.basic.singleSendContract": "Single Send contract", "templateDetail.basic.edit": "Edit", "templateDetail.basic.doc": "Document:", "templateDetail.basic.sender": "Contract sender:", "templateDetail.basic.senderAccount": "Contract sender:", "templateDetail.basic.receiver": "Contract recipient:", "templateDetail.basic.receiverAccount": "Contract recipient:", "templateDetail.basic.contractEndTime": "Contract expiration date:", "templateDetail.basic.signEndTime": "Sign expiration date:", "templateDetail.basic.endTimeUnit": "(Days)", "templateDetail.basic.contractType": "Contract type:", "templateDetail.basic.entNo": "Company's internal code:", "templateDetail.basic.area": "Area:", "templateDetail.basic.senderField": "Field to be filled in by the sender:", "templateDetail.basic.signerField": "Field to be filled in by the signatory:", "templateDetail.basic.contractInfo": "Contract information", "templateDetail.basic.pageIndex": "Number of pages: {page}", "templateDetail.basic.empty": "To be filled in", "templateDetail.basic.invalidStatementName": "Consistent with the original template", "templateDetail.msg.editSuccess": "Modify successfully", "templateDetail.msg.editFail": "Modify failed", "templateDetail.msg.whiteDocument": "Document content is empty", "templatePermission.tempMgmt": "Template management", "templatePermission.permissionMine": "My permissions", "templatePermission.permissionAll": "All permissions", "templatePermission.permissionAllTitle": "Authorization results", "templatePermission.withdrawPermissionAll": "Withdraw permissions", "templatePermission.withdrawPermissionDeny": "Withdraw permission denied", "templatePermission.admin": "Main administrator", "templatePermission.staff": "Employee", "templatePermission.myTemplatePermissions": "The template permissions of the current account include: ", "templatePermission.permissionLog": "Authorization logs", "templatePermission.new": "New", "templatePermission.search.composeName": "Name of permission combination", "templatePermission.search.auth": "Permission item", "templatePermission.search.name": "Name", "templatePermission.search.account": "Account", "templatePermission.search.role": "Role", "templatePermission.table.permissionFederationName": "Name of permission combination", "templatePermission.table.createPermissionFederation": "New permission combination", "templatePermission.table.btnTooltips": "The permission scheme allows you to create a set of permission controls and apply them to other templates. Only those who have been granted permissions can perform actions on the template.", "templatePermission.table.editPermission": "Edit permissions", "templatePermission.table.auth": "Permission item", "templatePermission.table.authTo": "Authorized to", "templatePermission.table.operate": "Operation", "templatePermission.table.authToEmployes": "Authorized personnel:", "templatePermission.table.authToRoles": "Authorized role:", "templatePermission.table.operateAuthTo": "Authorize", "templatePermission.table.time": "time", "templatePermission.table.detail": "detail", "templatePermission.table.distribute": " distribute", "templatePermission.table.withdraw": "withdraw", "templatePermission.table.permission": "permission", "templatePermission.table.deleteSelectTip": "Please select the federation to be delete", "templatePermission.table.resetPermission": "Re-authorize", "templatePermission.table.role": "Roles", "templatePermission.table.roleName": "Role/Member Name", "templatePermission.table.companyName": "Company", "templatePermission.table.permissionDetail": "Permission Details", "templatePermission.table.all": "All", "templatePermission.dialog.authToRoles": "Role", "templatePermission.dialog.selectEnt": "Select a company", "templatePermission.dialog.selectRole": "Select role", "templatePermission.dialog.selectedRole": "Selected role", "templatePermission.dialog.authToEmployes": "Personnel", "templatePermission.dialog.selectEmployes": "Select personnel", "templatePermission.dialog.selectedEmployes": "Selected personnel", "templatePermission.dialog.recoverEmployesPermission": "Retain existing personnel permissions", "templatePermission.dialog.recoverRolesPermission": "Retain existing role permissions", "templatePermission.dialog.keepPermissionTip": "Check to add new permissions on top of existing ones; uncheck to revoke existing permissions first, then grant permissions according to the new scheme (only affects selected roles or personnel, those not selected retain their original permissions)", "templatePermission.dialog.all": "Select all", "templatePermission.dialog.selectPermissionFederation": "Select authorization method", "templatePermission.dialog.confirm": "confirm", "templatePermission.dialog.cancel": "cancel", "templatePermission.dialog.deletePermissionTipContent": "Do you want to revoke the permissions of this role?", "templatePermission.dialog.deletePermissionTipContent1": "Do you want to revoke the permissions of this member?", "templatePermission.dialog.deletePermissionTipContent2": "Are you sure you want to revoke the permissions of this account/role", "templatePermission.dialog.tip": "Prompt", "templatePermission.slide.editComposeName": "Name of permission combination", "templatePermission.slide.editPermission": "Edit permissions", "templatePermission.slide.save": "Save", "templatePermission.slide.tip": "Modifying the permission scheme does not affect the permissions already assigned to members according to the original scheme. They will retain their original permissions.", "templatePermission.slide.nameNotEmpty": "Name of permission combination cannot be empty", "templatePermission.slide.checkNotEmpty": "The permissions checkbox cannot be empty", "templatePermission.slide.selectPermissionTitle": "Select authorization permissions", "templatePermission.msg.editSuccess": "Edit completed", "templatePermission.msg.editFail": "Edit failed", "templatePermission.msg.createSuccess": "Create successfully", "templatePermission.msg.createFail": "Create failed", "templatePermission.msg.unselectFederation": "Please select an authorization method first", "templatePermission.msg.deleteSuccess": "Delete completed", "templatePermission.msg.federationEmpty": "You do not have an authorization method yet. Please create one first.", "templatePermission.msg.permissionSuccess": "Authorization successful", "templatePermission.permissonAuthChoose.title": "Select the permission you want to withdraw", "templatePermission.permissonMember": "Members/Role Permissions", "templatePermission.mainAdminRole": "Main administrator", "templatePermission.employeeRole": "Employee", "templateConfigGuide.contract": "Contracts", "templateConfigGuide.hrContract": "HR contracts", "templateConfigGuide.salesContract": "Distributor contracts", "templateConfigGuide.userGuide": "Quick Guide", "templateConfigGuide.title": "Template Quick Setup Guide", "templateConfigGuide.sendContract": "Initiating contracts", "templateConfigGuide.funExplain": "Function Definitions", "templateConfigGuide.functionDes": "Template is the initial configuration of a contract used in a type of business, which can be configured with the contracting party, contracting conditions, and signing location, etc. Once configured, the template can be used in the next phase to send contracts to different companies/individuals by directly referring to these configuration conditions.", "templateConfigGuide.configUse": "Start setup", "templateConfigGuide.selectTemplateSceneType": "Select from the following templates according to the usage scenario of your contract to start the configuration.", "templateConfigGuide.hrScene": "HR scenario template", "templateConfigGuide.salesScene": "Distributor scenario template", "templateConfigGuide.commonScene": "Other scenario templates", "templateConfigGuide.sendContractDirect": "Quickly initiate a contract without creating a template", "templateConfigGuide.uploadFile": "Upload files (standard contract files are ready)", "templateConfigGuide.uploadTemplateFile": "Upload the contract template file", "templateConfigGuide.uploadingTip": "Please wait for the document upload to complete and then operate.", "templateConfigGuide.addContractTip": "You need to upload documents in \"Upload Files\" or set the number of blank document spaces to 1 in \"File Reservation\".", "templateConfigGuide.uploadFileDesc": "To upload a standard contract file (containing standard contract text content), you need to leave blank spaces in the file for the original contract content, which can be filled with specific content values by the sender or recipient to create a different contract in the future. Please click and upload the file.", "templateConfigGuide.sceneDoc.HR": "HR scenarios involve labor contracts and confidentiality agreements.", "templateConfigGuide.sceneDoc.SALES": "Distributor scenarios involve distribution agreements and cargo transportation contracts.", "templateConfigGuide.sceneDoc.COMMON": "General scenarios involve mainly general contracts.", "templateConfigGuide.filePreserve": "Document reservation (contract documents are not ready)", "templateConfigGuide.filePreserveDesc1": "If you do not have the contract documents ready, you can reserve document space for the documents by this step, so that the documents with several copies will take up corresponding spaces, and you can add prepared contract documents later when you use the contracts.", "templateConfigGuide.blankDocNumIs": "The number of documents to be uploaded is", "templateConfigGuide.fillDesField": "Fill in the contract description field", "templateConfigGuide.setDesFieldTip1": "The contract description field is convenient for you to record and manage the contract itself. After uploading the documents, fill in the contract description fields, such as contract type, internal company number, etc.", "templateConfigGuide.setDesFieldTip2": "Contract type, company's internal number, contract deadline, contract expiration date are the default description fields. You can click the colored font \"Field Settings\" to configure whether to show or hide these description fields.", "templateConfigGuide.contractTypeDesp1": "If you want to add contract type, you can go to \"", "templateConfigGuide.contractTypeDesp2": ", select the edit button of Contract Type\", and add the type description.", "templateConfigGuide.consoleRouteText": "Company Control Center - Business Field Management - Contract Description", "templateConfigGuide.configBusinessFieldInfo": "Customize contract description fields", "templateConfigGuide.descFieldDesp1": "In addition to the \"Contract Type\" description field, you can also customize other description fields, you can go to", "templateConfigGuide.descFieldDesp2": "setting customization", "templateConfigGuide.backToPage": "Back to the page", "templateConfigGuide.setContractType": "Fill in the contract type", "templateConfigGuide.setFieldNecessary": "Set the contract description field as required", "templateConfigGuide.setReceiverRole": "Set the contract role", "templateConfigGuide.receiverRoleExplain": "The contract role refers to the company or individual who needs to stamp/sign the contract.", "templateConfigGuide.backToSetTemplateName": "You need to go back to the interface to add information", "templateConfigGuide.hrTemplateRoleDesc": "The HR scenario has been configured with two roles by default.", "templateConfigGuide.salesRoleDesc": "The distributor scenario has been configured with two roles by default.", "templateConfigGuide.commonRoleDesc": "The generic scenario has one role configured by default.", "templateConfigGuide.changeRoleNameTip": "You can change the role name, or continue to add new roles", "templateConfigGuide.role": "Signing role {num}.", "templateConfigGuide.addPersonRole": "+ Add individual signing role", "templateConfigGuide.addEntRole": "+ Add company signing role", "templateConfigGuide.entPlaceholder": "e.g.: company, department", "templateConfigGuide.personPlaceholder": "e.g.: Employee", "templateConfigGuide.batchImportRoleInfo": "Bulk add contract roles", "templateConfigGuide.batchImportRoleInfoDesc": "If you need to send a batch of contracts to different companies/individuals when using the template, you can check the box \"Need to use Excel to batch import account mode\" after the corresponding role. The system will automatically match the contracting roles and account numbers in your Exce file. Please select the roles you want to send in bulk.", "templateConfigGuide.needExcelImport": "Need to use Excel to bulk import account numbers.", "templateConfigGuide.person": "Personal", "templateConfigGuide.ent": "Corporate", "templateConfigGuide.setRoleSignConfig": "Set up signing role requirements", "templateConfigGuide.setRoleSignConfigDesc": "You can set the signing method and signing requirements of the contracting role in advance. Please fill in directly if it has been specified, and add if it has not been specified when sending the contract using the template.", "templateConfigGuide.pointPosition": "Specify the signing position", "templateConfigGuide.pointPositionDesc": "In this step, you can set a placeholder in the blank space of the document, and select the type that matches from the \"Temporary fields\" on the left side of the page. On the right side of the page, you can specify the name of this placeholder and the person who will fill it in.", "templateConfigGuide.templateConfigDesc": "Your template now has basic usability. You can further enhance the functionality of the template through the following steps:Reasonably allocate various template permissions, ensuring they are assigned to appropriate personnel or roles;Access the template details page to complete further configuration work.", "templateConfigGuide.templateConfig": "Template permission settings", "templateConfigGuide.addContractField": "At the document stamping/signature place, you can assign the signing position for each signatory. Take a blank document as an example. You need to do the following", "templateConfigGuide.addSignFieldPosition": "If it is a blank document, you need to set the keywords contained in each signing color stamp/signature in the document in advance, and locate the signing position by the keywords.", "templateConfigGuide.templateConfigAuthTip": "Define template permissions in Template Management - Permission Management page", "templateConfigGuide.templateDetailTip": "Set up to deal with various scenarios in template detail page", "templateConfigGuide.toSetting": "Go to Settings", "templateConfigGuide.employee": "Employee", "templateConfigGuide.hr": "Company HR Department", "templateConfigGuide.dealer": "Dealer Enterprise", "templateConfigGuide.finance": "Company Finance Department", "templateConfigGuide.entOwn": "Own Enterprise", "templateFolder.add": "Create a new folder", "templateFolder.edit": "Edit the folder", "templateFolder.folder": "Folder name", "templateFolder.nameEmptyTip": "Please enter a folder name", "templateFolder.selectFolderTip": "Please select a folder", "templateFolder.groupSuccess": "Archived successfully", "templateFolder.nameInvalidTip": "Please enter the character combination of Chinese, English and numbers", "templateFolder.deleteFolderTip": "After deletion, the template in the folder will be released. Confirm deletion?", "templateFolder.deleteSucc": "Deleted successfully", "editCompose.edit": "Modify document combination", "editCompose.name": "Combination name", "editCompose.id": "Combination ID", "editCompose.comment": "Document remarks", "editCompose.selectDoc": "Select contract:", "editCompose.currentCompose": "Current document combination:", "editCompose.confirm": "OK", "editCompose.errorTip.nameNeed": "Please enter the name of the document combination", "editCompose.errorTip.idNeed": "Please enter the document combination number", "editCompose.errorTip.selectedNeed": "Please select at least 2 items", "templateList.manager": "Administrator", "templateList.managerTip": "Personnel with authority to allocate permissions.", "templateList.createTemp": "Create template", "templateList.createDynamicTemp": "Create dynamic template", "templateList.dynamicTemplateEntry": "Dynamic template portal", "templateList.myTemp": "My templates", "templateList.allTemp": "All templates", "templateList.myCreateTemp": "Created", "templateList.grantedTemp": "Authorized", "templateList.tempName": "Template name", "templateList.tempMark": "Template remarks", "templateList.tempId": "Template ID", "templateList.star": "Save template", "templateList.cancelStar": "Remove template", "templateList.starSuccess": "Template saved", "temolateList.starFolder": "Favorites", "templateList.templateIdTip": "Template ID format error, please enter numbers only", "templateList.cancelStarSuccess": "Template removed", "templateList.hasCacheDraft": "There is a contract temporarily saved at {time} that has not been sent. Do you want to continue?", "templateList.templateCategory": "Template type", "templateList.dynamicTemplate": "Dynamic template", "templateList.staticTemplate": "Template", "templateList.approvalTemp": "Approval required", "templateList.createUser": "Created by", "templateList.creatingCompany": "Template-Creating Company", "templateList.folder": "Group", "templateList.tempPermission": "Template permission", "templateList.permissionMgmt": "Permission management", "templateList.enableStatus": "Status of use", "templateList.enabled": "Enabled", "templateList.disabled": "Disabled", "templateList.batchPermission": "Bulk authorize", "templateList.permissionSuccess": "Authorize successfully", "templateList.selectPermission": "Please select a permission group", "templateList.selectFederation": "Select a permission combination", "templateList.selectRole": "Select role", "templateList.selectUser": "Select personnel", "templateList.enableTemp": "Enable the template", "templateList.disableTemp": "Disable the template", "templateList.enableConfirmText": "Once enabled, authorized users will be able to use the template. Do you want to enable it?", "templateList.disableConfirmText": "Once disabled, the template can not be used. Do you want to disable it?", "templateList.enableSuccess": "Enabled successfully", "templateList.disableSuccess": "Disabled successfully", "templateList.deleteTemp": "Delete the template", "templateList.deleteFail": "Delete failed", "templateList.deleteTempConfirmText": "The deleted template cannot be restored. Do you want to delete it?", "templateList.editInfoTip": "The template is still being edited. Please contact the template creator if you want to use it.", "templateList.enable": "Enable", "templateList.disable": "Disable", "templateList.switchToReceiver": "Switched to {receiver} ", "templateList.createUserPlaceholder": "Input must include the first part of the name", "templateList.sendCode": "Template sending code", "templateList.moreFeature": "Advanced feature", "templateList.downloadSendCode": "Download ", "templateList.generate": "Create", "templateList.view": "View", "templateList.close": "Close", "templateList.alwaysEnable": "Valid over the long term", "templateList.customDays": "Customized number of days", "templateList.remainDays": "{day} days remaining", "templateList.selectValidDate": "Select expiration date:", "templateList.selectValidDateTip": "Please select expiration date", "templateList.selectValidDateErrorTip": "Please select a time after the current time", "templateList.validToDate": "Valid until:", "templateList.inputIntNumberPlaceholder": "Please select a time within 999 days.", "templateList.setExpireDays": "Set the number of days available for the template.", "templateList.noPermissions": "You need to obtain template editing permission before you can make changes", "templateList.sendCodeTip": "The current template settings do not meet the conditions for generating the send-code. Check whether the following requirements are met:", "templateList.errorOperate": "Wrong permission operation", "templateList.sendCodeTipFail.1": "No blank documents are included", "templateList.sendCodeTipFail.2": "The contracting party has only one variable party (including signature and copy), and the variable party must be the first operator; The signatory must have a signature stamping position", "templateList.sendCodeTipFail.3": "The fixed account of the contracting party shall not be empty", "templateList.sendCodeTipFail.4": "Does not trigger pre-shipment approval", "templateList.sendCodeTipFail.5": "The contents that the sender has to fill are not empty(Include the description field and the contract content field)", "templateList.sendCodeTipFail.6": "Not template combination", "templateList.sendCodeGuide.title": "Advanced function description of the code for sending contract.", "templateList.sendCodeGuide.info": "Signing QR code is applicable to contract documents that do not require the sender to fill in the content, such as employment termination certificate, confidentiality agreement, power of attorney, etc., which can be provided to any people who scan the QR code for signing.If there is information in the document that the sender must fill in, or the sender needs to limit the scope of the parties to whom the QR code can be scanned to obtain the document, the advanced function of File+ can be used. By scanning the QR code of the file cabinet, the contract can be automatically sent to designated personnel, and the content that needs to be filled in by the sender can be completed. Even the time of automatic delivery can be set. The counterparties who has scan the code will also be stored in the file cabinet, and can be checked at any time. Specific methods of use are as follows:", "templateList.sendCodeGuide.tip1.main": "1. BestSign", "templateList.sendCodeGuide.tip1.sub": "", "templateList.sendCodeGuide.tip1.line1": "Apply to BestSign for activate the file+ module, contract pre-approval and intelligent pre-approval.", "templateList.sendCodeGuide.tip1.line2": "After enabled, you can go to the corresponding menu to operate and use the features.", "templateList.sendCodeGuide.tip2.main": "2. File cabinet administrator Create a file cabinet and configure intelligent pre-approval.", "templateList.sendCodeGuide.tip2.sub": "Create a file cabinet and configure intelligent pre-approval.", "templateList.sendCodeGuide.tip2.line1": "", "templateList.sendCodeGuide.tip2.line2": "Create the same fields in the file cabinet as the fields in the contract, bind the contract template, set the corresponding relations of the fields and the conditions for the automatically issuing when scan the QR code, and provide the QR code of the file cabinet to the contracting party.", "templateList.sendCodeGuide.tip3.main": "3. Signatory", "templateList.sendCodeGuide.tip3.sub": "Scan the QR code to fill in information and obtain contract documents.", "templateList.sendCodeGuide.tip3.line1": "", "templateList.sendCodeGuide.tip3.line2": "The contracting party scans the QR code provided by the issuing party to fill in the information required, and the information collected through the file cabinet will be archived and automatically filled into the contract. The signatory receiving the contract only needs a simple stamp or signature to complete contract signing.", "templateList.sendCodeGuide.tip4.main": "4. File cabinet administrator", "templateList.sendCodeGuide.tip4.sub": "", "templateList.sendCodeGuide.tip4.line1": "Check the status of signatory and contract sending.", "templateList.sendCodeGuide.tip4.line2": "The administrator of the sender can go to the file cabinet and check the information of the signatory who has scanned the QR code, the status of contract delivery, whether the signing is completed and so on.", "templateList.linkBoxTip": "Associated Cabinet ID：", "templateApproval.approvalProcess": "Approval Process", "templateApproval.approvalStatus": "Approval status", "templateApproval.approving": "Under Approval", "templateApproval.toBeApproval": "Approval Required", "templateApproval.approved": "Approved", "templateApproval.approver": "Approver", "templateApproval.reporter": "Submitter", "templateApproval.reject": "Reject", "templateApproval.agree": "Approve", "templateApproval.backList": "Back to List", "templateApproval.signerInfo": "Signer information", "templateApproval.rejectSuccess": "Rejection Successful", "templateApproval.approvalSuccess": "Approval Completed", "templateApproval.approvalStopTip": "The approver or content pending approval has changed. Please return to \"Pending My Approval\" and re-enter the template approval page.", "templateApproval.approvalAttention": "There may be a delay of a few seconds for new submissions. Please refresh later to view the review status.", "templateApproval.approvalMessage": "Approval Comments：", "templateApproval.approvalAgree": "<PERSON><PERSON><PERSON><PERSON> Passed", "templateApproval.approvalReject": "<PERSON><PERSON><PERSON><PERSON> Rejected", "templateApproval.templateDocumentsInfo": "Template Document Information", "templateApproval.templateDocumentName": "Template Document Name", "templateApproval.templateDocumentId": "Template Number(documentId)", "templateApproval.templateApprovingTip": "Templates under approval cannot perform this operation.", "templateListEntDialog.title": "Bulk template authorize", "templateListEntDialog.entGroupNums.1": "The selected template belongs to ", "templateListEntDialog.entGroupNums.2": "different companies. Please authorize them in turn.", "templateListEntDialog.authed": "Authorized", "templateListEntDialog.next": "Next", "templateListEntDialog.confirm": "OK", "templateSubList.tempDocument": "Template document", "templateSubList.tempFederation": "Document combination", "templateSubList.templateSpecialSeal": "Special seal for template", "templateSubList.tempSceneConfig": "Template scenario", "templateSubList.tempFederationTip": "Click here to view the document portfolio.", "templateSubList.tempInvalidStatement": "Void statement", "templateSubList.tempSupplement": "Supplementary agreement", "templateSubList.documentName": "Document name", "templateSubList.federationId": "Combination ID", "templateSubList.noFederationData": "No file combinations have been set for the template", "templateSubList.federationOptTip1": "Configure file combinations on tab page of the ‘template file’", "templateSubList.federationOptTip2": "Quickly send the file combination while sending the contract", "templateSubList.federationName": "Combination name", "templateSubList.useFederation": "Select and use", "templateSubList.createFederation": "Form a document combination", "templateSubList.federationMark": "Combination remarks", "templateSubList.inputFederationName": "Please enter the name of the combination", "templateSubList.addTempFederation": "Add a document combination", "templateSubList.addTempIsAutoJoin": "Do you want the newly uploaded documents to be automatically added to this document combination?", "templateSubList.delete": "Delete", "templateSubList.deleteDocument": "Delete a document", "templateSubList.deleteDocumentTipDesc": "Whether to delete the document, which cannot be recovered after deletion", "templateSubList.confirmTitle": "", "templateSubList.deleteSuc": "Deleted successfully!", "templateSubList.deleteFederationTipDesc": "Delete this combination? Cannot be recovered after deletion", "templateSubList.sceneConfig.saveConfig": "Save configuration", "templateSubList.sceneConfig.saveSuccess": "Saved successfully", "templateSubList.sceneConfig.limitedTitle": "Restrictions:", "templateSubList.sceneConfig.manageVerifyCode.showCodeInContract": "Show the verification code in the contract", "templateSubList.sceneConfig.manageVerifyCode.selectCodeStyle": "Please select the style of the verification code", "templateSubList.sceneConfig.manageVerifyCode.preView": "Preview", "templateSubList.sceneConfig.manageVerifyCode.limitOtherSignatureTip": "Display of verification code in contract is not applicable to cross-platform signatures. Please disable \"cross-platform signature\" configuration before operation.", "templateSubList.sceneConfig.manageVerifyCode.selectCodePage": "Please select the location of the verification code display", "templateSubList.sceneConfig.manageVerifyCode.lastPage": "The last page", "templateSubList.sceneConfig.manageVerifyCode.allPage": "All pages", "templateSubList.sceneConfig.manageVerifyCode.allPageTip": "Please keep the size of each page of the contract consistent before displaying all the verification code. It is recommended to use the vertical A4 documents.", "templateSubList.sceneConfig.INNER_RESOLUTION.title": "Internal resolution scenario", "templateSubList.sceneConfig.INNER_RESOLUTION.description": "Function effect: Multiple signing roles are set up for corporate signatories to sign on behalf of the company + 1 signing role to seal on behalf of the company. When the company seals, the signing roles of the company that does not need to continue to sign (the signed signatures are still in effect).", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.0": "Hidden rejection button must be enabled.", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.1": "Does not apply to \"sequential signing\" scenarios.", "templateSubList.sceneConfig.INNER_RESOLUTION.limitItems.2": "Does not apply to \"Allow paper signing instead\" scenarios.", "templateSubList.sceneConfig.INNER_RESOLUTION.hideRefuseBtnConfigTip": "To enable internal resolutions, you must first enable the Hidden Rejection button.", "templateSubList.sceneConfig.INNER_RESOLUTION.limitPageSignTip": "Internal resolutions do not apply to paper signing scenarios. Please disable the \"Allow paper signing instead\" configuration first.", "templateSubList.sceneConfig.INNER_RESOLUTION.limitHideRefuseTip": "Paper signing is not applicable to internal resolution scenarios. Please disable the \"Internal resolution scenario\" configuration first.", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.title": "Cross-platform signing", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.description": "Allow uploading PDF with digital certificate when using templates (i.e. contracts that have been signed on other signing platforms)", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.0": "Does not apply to the \"Show verification code in contract\" scenario", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.1": "Not applicable to \"contract decoration\" application scenario, including watermarks, or picture fields.", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.2": "Not applicable to \"fill in content fields (sender or signer)\" scenario", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.limitItems.3": "Not applicable to \"contract attachment function\"", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.note": "Tips: With the \"template special seal\" function, you can also control the signing behavior after the initiation of \"cross-platform inter-signing\".", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.conflictVerifyCode": "The cross-platform signing is not applicable to the verification code scenario. Please disable the \"Show verification code in contract\" configuration first.", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.updateHybridJarTip": "The version of Jar package is too low, please contact BestSign technical support to upgrade.", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent1": "PDFs containing digital certificates cannot be used as template documents. | Templates without Cross-Platform Digital Signing enabled cannot use files with digital certificates.", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent2": "Recommendations:", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent3": "Step 1: Create a template and enable \"Cross-Platform Digital Signing\" in \"More Settings - Scenario Customization\" (if this configuration is not available, you need to upgrade to a paid version);", "templateSubList.sceneConfig.OTHER_SIGNATURE_SUPPORT.tipContent4": "Step 2: When sending a contract using this template, upload a PDF with digital certificates to implement cross-platform digital signing.", "templateSubList.sceneConfig.paperSign.changeToPaperSign": "Allow paper signing instead.", "templateSubList.sceneConfig.paperSign.hideOtherAccountLogin": "Hide the use of other accounts to sign: The hidden portal may cause obstacles to signing, so this function is only applicable to in-person signing and signing by both parties. Please hide it with caution.", "templateSubList.sceneConfig.paperSign.signTitle": "Corporate signatories can sign contracts using stamped paper contracts that are mailed and without using electronic signatures.", "templateSubList.sceneConfig.paperSign.signTip": "The legal validity of paper signing is guaranteed by traditional methods and cannot be guaranteed by this platform. Please pay attention to the collection and preservation of evidence.", "templateSubList.sceneConfig.paperSign.postAddress": "Mailing address.", "templateSubList.sceneConfig.paperSign.receiverName": "Name of the recipient for the mail.", "templateSubList.sceneConfig.paperSign.receiverContract": "Mailing recipient's contact information.", "templateSubList.sceneConfig.paperSign.postAddressFill": "The specific address of the sender's company, optional", "templateSubList.sceneConfig.paperSign.receiverNameFill": "Name of the recipient of the paper contract from the sender, optional", "templateSubList.sceneConfig.paperSign.receiverContractFill": "Cell phone number of the recipient of the paper contract from the sender, optional", "templateSubList.sceneConfig.paperSign.preview": "Preview", "templateSubList.sceneConfig.paperSign.pdfRequired": "The signatory must provide a scanned copy of the paper contract (PDF)", "templateSubList.sceneConfig.paperSign.paperErrorTip.receiverName": "Please enter the correct name of the recipient", "templateSubList.sceneConfig.paperSign.paperErrorTip.receiverInfo": "Contact information is wrong. Please check and fill in again", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.notice": "Caution:", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip1": "The first signatory of the contract has used paper signing method, then the unsigned signatory must also use paper signing method.", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip2": "If each signatory of the contract has not used paper signing, but the unsigned signatory has gone through BestSign's real name authentication, then the signatory must use electronic signatures.", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip3": "The signatory of a contract using paper signature does not need to pass real name authentication, but only needs to use the signature verification code to sign the contract.", "templateSubList.sceneConfig.paperSign.paperSignNotAllowTip.tip4": "It is recommended to configure sequential signatures.", "templateSubList.sceneConfig.paperSign.saveConfig": "Save paper signing settings", "templateSubList.sceneConfig.paperSign.advance": "Advanced requirements for paper signing.", "templateSubList.sceneConfig.paperSign.advanceRadioText.freeChoice": "Free choice: The signatory can choose to use paper signing or electronic signing.", "templateSubList.sceneConfig.paperSign.advanceRadioText.mustUsePaperSign": "Mandatory paper signing: All signatories must use paper signing instead of electronic signing.", "templateSubList.sceneConfig.paperSign.advanceRadioText.separateConfig": "Separate configuration of paper signing: The default use is electronic signing. \"Paper signing required\" can be configured separately for the contract. That is: after the sender turns on \"paper signing required\" function in a contract, the signatory of the contract must use paper signing, while other contracts not configured as such must still use electronic signing.", "templateSubList.sceneConfig.paperSign.openSequence": "Turn on sequential signing", "templateSubList.sceneConfig.paperSign.cancel": "Cancel", "templateSubList.sceneConfig.paperSign.confirm": "Confirm", "templateSubList.sceneConfig.limitInnerResolutionTip": "The internal resolution scenario depends on the \"hide the rejection\" button. Please disable the \"internal resolution scenario\" configuration first.", "templateSubList.sceneConfig.limitSettingTip": "Please check the setting instructions in the right sidebar first", "templateSubList.sceneConfig.openSuccess": "Successfully turned on", "templateSubList.sceneConfig.closeSuccess": "Successfully turned off", "templateSubList.sceneConfig.hideRefuseSignButton": "Hide reject button", "templateSubList.sceneConfig.viewCase": "View case", "templateSubList.sceneConfig.enableContractAlias": "Enable \"Contract\" alias", "templateSubList.sceneConfig.contractAliasDesc": "When the contract recipient signs this template contract, the word \"Contract\" is replaced by the following text.", "templateSubList.sceneConfig.hideSenderInfoInPreviewPage": "Hide sender info in contract preview page", "templateSubList.sceneConfig.isAutoSignConfigOn": "Whether to allow the contracting party to enable the \"auto-sign\" feature to sign contracts sent from this template", "templateSubList.sceneConfig.autoSign": "Auto-sign control for signatories", "templateSubList.sceneConfig.selfEntAccessOnly": "Only our companies/groups are allowed to use auto-stamping (including signatures sent to companies, but not to individuals), no other contract signatories are allowed to auto-sign.", "templateSubList.sceneConfig.close": "Close", "templateSubList.sceneConfig.save": "Save", "templateSubList.sceneConfig.signRoleEnable": "The following contracting roles can also use auto-signing.", "templateSubList.sceneConfig.allSignerNoAccess": "All contracting parties are not allowed to auto-sign", "templateSubList.sceneConfig.autoSignNotAllowTip.notice": "After you set the conditions for the signatory to sign automatically, you also need the signatory to enable the automatic signature function to finalize automatic contract signing.", "templateSubList.sceneConfig.autoSignNotAllowTip.title": "Contractors configured with the following signing requirements cannot auto sign contracts sent from this template:", "templateSubList.sceneConfig.autoSignNotAllowTip.tip1": "Handwritten signatures are required", "templateSubList.sceneConfig.autoSignNotAllowTip.tip2": "Must be signed with facial verification", "templateSubList.sceneConfig.autoSignNotAllowTip.tip3": "The receiving party shall pay", "templateSubList.sceneConfig.autoSignNotAllowTip.tip4": "The signatory shall fill in the fields", "templateSubList.sceneConfig.autoSignNotAllowTip.tip5": "The signatory shall submit information", "templateSubList.sceneConfig.autoSignNotAllowTip.tip6": "Handwritten note recognition", "templateSubList.sceneConfig.autoSignNotAllowTip.tip7": "Read before signing", "templateSubList.sceneConfig.autoSignNotAllowTip.tip8": "The real name of the operator needs to be verified, but the real name information of the operator is inconsistent or it is not the real name.", "templateSubList.sceneConfig.autoSignNotAllowTip.tip9": "Business verification seal", "templateSubList.sceneConfig.autoSignNotAllowTip.tip10": "Multiple signatures from the company", "templateSubList.sceneConfig.autoSignNotAllowTip.tip11": "FDA signature", "templateSubList.sceneConfig.specificationBusinessFields": "Standardize the business fields of the contract", "templateSubList.sceneConfig.descriptionField": "Contract description field", "templateSubList.sceneConfig.contentField": "Contract content field", "templateSubList.sceneConfig.addField": "New fields", "templateSubList.sceneConfig.tip": "Tips", "templateSubList.sceneConfig.deleteConfirmTip.0": "After deletion, the fields set in the template will disappear. You can directly modify the field names, and the attributes of the fields set in the template will remain unchanged, but the field names will use the new names.", "templateSubList.sceneConfig.deleteConfirmTip.1": "Continue to delete?", "templateSubList.sceneConfig.deleteSuc": "Deleted successfully", "templateSubList.sceneConfig.fda.enableFdaSignature": "Enable FDA signature style", "templateSubList.sceneConfig.fda.description": "When checked, the signature complies with FDA 21 CFR Part 11 and is valid for corporate signatures and individual signatures", "templateSubList.sceneConfig.fda.addOption": "Add options", "templateSubList.sceneConfig.fda.allowInput": "Allow signatories to enter their own", "templateSubList.sceneConfig.fda.allowInputTip": "Check the box to allow the signatories to edit the Reason", "templateSubList.sceneConfig.fda.inputOption": "Please enter options", "templateSubList.sceneConfig.fda.inputOptionLimit": "Please enter content at a length of less than {limit}", "templateSubList.sceneConfig.signerWatch.title": "签署关键人跟踪", "templateSubList.sceneConfig.signerWatch.target": "跟进人：", "templateSubList.sceneConfig.signerWatch.sender": "发件人", "templateSubList.sceneConfig.signerWatch.CC_USER": "抄送人", "templateSubList.sceneConfig.signerWatch.SIGNER": "签署人", "templateSubList.sceneConfig.signerWatch.COMPLETE": "补全", "templateSubList.sceneConfig.signerWatch.role": "签约角色：", "templateSubList.sceneConfig.signerWatch.configResult": "When the configuration is completed, the Follower will receive an email after the concerned role signing the contract.", "templateSubList.sceneConfig.signerWatch.emailContent": "The message title is:“Contract Name - Signer Name has signed”", "templateSubList.sceneConfig.signerWatch.configTipHead": "Tips:", "templateSubList.sceneConfig.signerWatch.setEmailTip": "1) Please remind the colleagues who may receive the email of configuring the Notification Email in advance (if the login account is already an email, They don't need to configure it).", "templateSubList.sceneConfig.signerWatch.limitedTip": "2) When the followers are the signers of the front desk collection or cross business line, they can't receive these emails.", "templateSubList.sceneConfig.signerWatch.signOrderlyTip": "3) This function does not support Non-sequential-signing. Please OPEN Sequential-Signing when using this template.", "templateSubList.sceneConfig.customsCheck": "Customs signing file detection, assisting in checking whether the file meets the customs signing standards (detecting the length of the file name, font, and whether there are blank pages)", "templateSubList.sceneConfig.enterpriseSignatureOnly": "Allow company signature to be used separately", "templateSubList.sceneConfig.isSwitch": "Switch", "templateSubList.sceneConfig.featureDescription": "Feature Description", "templateSubList.sceneConfig.featureConfiguration": "Feature Configuration", "templateSubList.invalidStatement.description.0": "The status of the contract can be changed to \"Voided\" through Void Statement. The Void Statemen will be released to all persons (including those who have not participated in the contract) through: ", "templateSubList.invalidStatement.description.1": "(1) The voided contract is displayed on the page displaying the signature verification results after the signature verification on BestSign official website.", "templateSubList.invalidStatement.description.2": "(2) Open the contract inspection page by scanning the contract inspection QR code on the voided contract.", "templateSubList.invalidStatement.reset": "Reset void statement", "templateSubList.invalidStatement.delete": "Delete", "templateSubList.invalidStatement.page": " Page", "templateSubList.invalidStatement.create": "Set void statement", "templateSubList.invalidStatement.deleteTip": "Deleted successfully", "templateSubList.invalidStatement.deleteConfirm": "After deletion, you will not be able to complete the contract cancellation process in BestSign's system. Proceed to delete?", "templateSubList.invalidStatement.deleteConfirmTip": "Tips", "templateSubList.invalidStatement.editName": "Modify the names", "templateSubList.invalidStatementTabTip": "Please set the contracting party in the contract template first.", "templateSubList.specialSeal.templateRole": "Template role", "templateSubList.specialSeal.isLimitEnable": "Whether to enable the special seal", "templateSubList.specialSeal.configLimitInfo": "Set the special seal", "templateSubList.specialSeal.configLimitInfoOwn": "Set your own special seal", "templateSubList.specialSeal.configLimitInfoOther": "Set the special seal of the other party", "templateSubList.specialSeal.settingTip": "Tips", "templateSubList.specialSeal.companySealSetTip": "A special seal has been set for the company. Whether to replace the original special seal?", "templateSubList.specialSeal.companySealSetTip1": "The following companies have already set up a special seal:", "templateSubList.specialSeal.companySealSetTip2": "Do you need to update the special seal set for this time?", "templateSubList.specialSeal.deleteTip": "Delete the special seal for the template", "templateSubList.specialSeal.confirmTip": "Confirm to delete the special seal for the template?", "templateSubList.specialSeal.enable": "Enabled", "templateSubList.specialSeal.disable": "Not enabled", "templateSubList.specialSeal.config": "View Edit", "templateSubList.specialSeal.sealImgSource": "Source of the special stamp ", "templateSubList.specialSeal.fromTemplate": "Seal from template", "templateSubList.specialSeal.fromContract": "Seal from the contract", "templateSubList.specialSeal.sourceIdInputTip": "Please enter the template number or contract number", "templateSubList.specialSeal.inputIdText": "Please enter {text}", "templateSubList.specialSeal.templateNo": "Template number", "templateSubList.specialSeal.contractNo": "Contract number", "templateSubList.specialSeal.limitedSealsList": "Current special seal list", "templateSubList.specialSeal.newLimitSealsList": "The current new special seal", "templateSubList.specialSeal.limitSealTip": "Existing special seal patterns will not be updated. If you want to update, you need to delete the special seal first.", "templateSubList.specialSeal.noSealTip": "There is no contract-related seal sent from this template|There is no contract-related seal", "templateSubList.specialSeal.correctNoTip": "Please enter the correct {text}", "templateSubList.specialSeal.confirm": "Confirm", "templateSubList.specialSeal.cancel": "Cancel", "templateSubList.specialSeal.save": "Save", "templateSubList.specialSeal.saveSuccess": "Saved successfully", "templateSubList.specialSeal.removeSuccess": "Deleted successfully", "templateSubList.specialSeal.sameSealTip": "This seal has been set", "templateSubList.specialSeal.companyName": "Company", "templateSubList.specialSeal.plsInputEnterpriseName": "Please enter the company name", "templateSubList.supplement.add": "Add supplement contracts", "templateSubList.supplement.inputTemplateId": "Please enter the template ID to be supplemented", "templateSubList.supplement.inputCorrectTip": "Please enter the correct template ID number", "templateSubList.contractConfidentiality.name": "Contract confidentiality", "templateSubList.contractConfidentiality.autoTransferDesc": "After the contract participants of the group/enterprise complete the contract operation task, they will automatically transfer the contract to the new account for holding, and the original contract participants will no longer hold the contract.", "templateSubList.contractConfidentiality.viewTemplate": "View schematic", "templateSubList.contractConfidentiality.holderDesc": "Note: The account number of the new holder must be in the same enterprise as the original contract holder to take effect.", "templateSubList.contractConfidentiality.holderTip": "(Contract holder: the account number of the enterprise/group member who participated in the contract, including sending, approving, signing, completing and being copied)", "templateSubList.contractConfidentiality.participantsType": "Participation mode", "templateSubList.contractConfidentiality.newHolderAccount": "Account of new holder", "templateSubList.contractConfidentiality.accountPlaceholder": "Mobile phone/email, no transfer if not filled in", "templateSubList.contractConfidentiality.sendContract": "Send the contract", "templateSubList.contractConfidentiality.approvalContract": "Approve the contract", "templateSubList.contractConfidentiality.signContract": "Sign the contract", "templateSubList.contractConfidentiality.editContract": "Complete the contract", "templateSubList.contractConfidentiality.editContractTip": "Reserve fields in the contract and complete the field content after sending the contract. This participation method is not used in most businesses and generally does not need to be configured.", "templateSubList.contractConfidentiality.hideContractDesc": "Automatically move into the safe: the contract is uniformly entered into the \"contract safe\". The contract details can only be viewed by the holder, and the administrator who does not hold the contract cannot view it. Unless the contract itself needs to be confidential, it is not recommended to enable this function. Because it will affect the normal statistics and search of contracts, and will also affect API calls to such contracts.", "sendContract.tempSaveTip.title": "\"Stage and Exit\" button", "sendContract.tempSaveTip.content1": "The contract has not been sent yet. When you select this template again to send a contract, you can continue sending this contract. ", "sendContract.tempSaveTip.content2": "One template can be stored temporarily for you at most, and you can continue to send it within 7 days. ", "upload.addBlank": "Add document to be uploaded", "upload.addLocal": "Upload local documents", "upload.addBill": "Add business document", "upload.addWordBill": "Add Word Document Contract", "upload.exampleAsFollows": "Examples as follows:", "upload.wordBillTip1": "Supports Word document types to dynamically generate new documents based on filled content. | The sender or signer's fields are defined and distinguished by whether there is an @ in the document", "upload.wordBillTip2": "The uploaded Word document needs to have positioning fields added in advance (field names are customizable)", "upload.billTip1": "Support Excel to dynamically synthesize new documents with the content transferred from the interface. |The location field (user-defined field name) should be added to the uploaded excel in advance, and the parameter name of the sending contract interface should be consistent with the field name in the template.", "upload.field1": "{{field name}}", "upload.field2": "{#field name}", "upload.wordBillfield": "Detail fields: System automatically inserts new data (adds rows downward), use [field name] with square brackets", "upload.billTip2": "1. For the basic field, fill a cell with data (horizontal filling), and use {text}, that is, double brackets. | 2.  The detail field is used to mark the automatic insertion of new data (new rows are added downward), using {text}, that is, single bracket plus # mark. | 3. An example is as follows: After uploading, send the contract through the interface.", "upload.billTip": "Tips: It supports excel type documents to integrate new documents based on the content input through the interface.", "upload.newBillTip1": "1. Basic fields, data is filled into a single cell (horizontal filling)", "upload.newBillTip2": "2. Detail fields, used to mark automatic insertion of new data (adding new rows downward)", "upload.newBillTip3": "Sender: Basic fields are represented by {{field name}}; Detail fields are represented by [field name]", "upload.newBillTip4": "Signer: Basic fields are represented by {{@field name}}; Detail fields are represented by [@field name]", "upload.uploadDoc": "Upload document", "upload.typeTips": "jpg, png, doc, docx, and pdf formats are supported", "upload.dynamicTypeTips": "doc and docx formats are supported by the file", "upload.gamaTypeTips": "File format supported: PDF and images, unsupported format: Word, Excel", "upload.newBlank": "Add document to be uploaded", "upload.modifyTItle": "Modify Contract Title", "upload.needUpdateTitle": "Use the document name as the contract title. If checked, the new uploaded document name will be used as the contract title; if unchecked, the configured \"Contract Title\" will be used.", "upload.docTitle": "Contract title", "upload.inputDocTitle": "Please enter a contract title", "upload.fileLessThan": "Please upload a file smaller than {num}M", "upload.usePdf": "Please upload PDF files or images", "upload.useDoc": "Please use DOC files or images when uploading", "upload.useExcel": "Please use XLSX or XLS files when uploading", "upload.fileNameMoreThan": "Please upload a file smaller than {num}M", "upload.blankTips": "You can set up a template without uploading documents for now, and then fill in the documents when sending the contract using the template. Document to be uploaded are only used for 'placeholder' purposes.", "upload.createContract": "Add more documents", "upload.fileNameTips.0": "The \"Contract Title\" set here serves two purposes: ", "upload.fileNameTips.1": "1. If there are multiple documents, it can be used as a label for each document, making it easier to locate the documents;", "upload.fileNameTips.2": "2. It can be used as the default contract title when sending the contract.", "upload.fileNameReplace": "After uploading the document, take the document name as the contract document title.", "addressBook.searchAll": "Select all", "addressBook.innerMember.title": "Internal members of the company", "addressBook.innerMember.tips": "Adjust company member information so that senders can find internal contacts faster", "addressBook.innerMember.operation": "Go to the Console", "addressBook.outerContacts.title": "External company contacts", "addressBook.outerContacts.tips": "Invite your business partners to finish the real names authentication in advance for rapid business development", "addressBook.outerContacts.operation": "Invite your partners", "addressBook.myContacts.title": "My contacts", "addressBook.myContacts.tips": "Modify contacts to ensure accurate signatory information", "addressBook.myContacts.operation": "Go to Admin", "addressBook.myContacts.toConsole": "Go to the Console", "addressBook.selected": "Selected accounts", "addressBook.search": "Search", "addressBook.all": "All", "addressBook.loadMore": "Load more", "addressBook.end": "All loaded", "addressBook.resigned": "Resigned", "addressBook.editOutContact": "Modify external contact information", "addressBook.noEditPermission": "No permission, please contact the main administrator", "addReceiver.prepareReceiver": "Pre-set signatory", "addReceiver.viewSignOrders": "Signing order diagram", "addReceiver.sender": "Sender", "addReceiver.receiver": "Mobile / Email for Recipients | (Up to 5, please separate with semicolons)", "addReceiver.orderSignLabel": "Sequential signing", "addReceiver.contactAddress": "Contact address book", "addReceiver.proxyOuterSend": "代理外部企业发送合同", "addReceiver.proxyOuterSendTip": "勾选后，可选择授权了代理发送权限的外部企业发合同", "addReceiver.noOuterSenderTip": "没有授权代理发送的外部企业，可以到档案柜提醒对方完成授权", "addReceiver.waitSenderInfoInit": "Sender data is being initialized, please try again later", "addReceiver.signInOrder": "Signing in order", "addReceiver.signInOrderTip": "When checked, all signatories sign in the set order. Each party can only sign after the previous one has completed.", "addReceiver.innerSigner": "Chinese Contracting Party", "addReceiver.internalSigner": "International Contracting Party", "addReceiver.configForReviewerBehind": "Set up for the following signatories:", "addReceiver.signOrder": "Sequence", "addReceiver.account": "Account", "addReceiver.accountPlaceholder": "Mobile phone number / Email address (required)", "addReceiver.accountReceptionCollection": "Received by the signatory's reception desk", "addReceiver.accountReceptionCollectionTip1": "Don't know the signatory's account or the signatory doesn't have an account yet.", "addReceiver.accountReceptionCollectionTip2": "Please choose \"received by reception desk\" mode", "addReceiver.signSubjectPerson": "Signatory: Individual", "addReceiver.nameTips": "Full name (optional, for identity verification)", "addReceiver.requiredNameTips": "Full name (required, for identity verification)", "addReceiver.entOperatorNameTips": "Name (optional)", "addReceiver.needAuth": "Real name authentication required", "addReceiver.signSubjectEnt": "Signatory: Company", "addReceiver.entNameTips": "Enterprise name (Optional)", "addReceiver.operator": "Handler", "addReceiver.sign": "Sign", "addReceiver.done": "Done", "addReceiver.more": "More", "addReceiver.messageAndFaceVerify": "Face scan + verification code ", "addReceiver.messageAndFaceVerifyTips": "The user needs to complete the face scan and verification code before signing the contract. The user needs to complete real name authentication before face scan. These are only applicable to mainland residents.", "addReceiver.faceFirst": "Priority brushing, backup verification code signing", "addReceiver.faceFirstTips": "The signatory signs with facial verification by default, and automatically switches to SMS code verification when the number of failed facial verification reaches the daily limit", "addReceiver.mustFace": "Must sign with facial verification", "addReceiver.handWriteNotAllowed": "Handwritten signatures are not allowed", "addReceiver.mustHandWrite": "Must sign with handwritten signature", "addReceiver.fillIDNumber": "Identity document number", "addReceiver.fillNoticeCall": "Number of mobile phone receiving notifications", "addReceiver.fillNoticeCallTips": "Please fill in the number of the mobile phone receiving the notification", "addReceiver.addNotice": "Add signing instructions", "addReceiver.attachTips": "Contract attachments", "addReceiver.faceSign": "Must sign with facial recognition", "addReceiver.faceSignTips": "The signatory needs to finish facial verification to complete signing (signing with facial verification is only available for Mainland residents of China).", "addReceiver.handWriteNotAllowedTips": "Handwriting is required for this signer to complete", "addReceiver.handWriteTips": "Handwritten signature is required for this signatory to complete signing", "addReceiver.idNumberTips": "Used for signing identity check", "addReceiver.verifyBefore": "Verify the signatory's identity before he or she can preview the contract", "addReceiver.verify": "Identity verification", "addReceiver.verifyTips": "Up to 20 words", "addReceiver.verifyTips2": "You must provide this verification information to this user", "addReceiver.sendToThirdPlatform": "Send to third-party platforms", "addReceiver.platFormName": "Platform name", "addReceiver.fillThirdPlatFormName": "Please enter the name of the third-party platform", "addReceiver.attach": "Attachment", "addReceiver.attachName": "Attachment name", "addReceiver.exampleID": "Example: ID card photo", "addReceiver.attachInfo": "Attachment description", "addReceiver.attachInfoTips": "Example: Please upload my ID card photo (optional)", "addReceiver.addAttachRequire": "Add attachment requirements", "addReceiver.addSignEnt": "Add a company signatory", "addReceiver.addSignPerson": "Add an individual signatory", "addReceiver.addCC": "添加抄送方", "addReceiver.addCCEnt": "抄送给企业成员", "addReceiver.addCCPerson": "抄送给个人用户", "addReceiver.addCCUser": "Add Cc", "addReceiver.addSignUser": "add signature", "addReceiver.addFromAddressBook": "Add from address book", "addReceiver.selectContact": "Select contact", "addReceiver.save": "Save", "addReceiver.searchVerify": "Search verification", "addReceiver.fillImageContentTips": "Please fill in the image content", "addReceiver.ok": "OK", "addReceiver.findContact": "Find the following signatories from the contract", "addReceiver.signer": "Signatory", "addReceiver.signerTips": "Tip: After selecting the signatory, the platform can help locate the signature or stamp places.", "addReceiver.add": "Add", "addReceiver.notAdd": "Don't add", "addReceiver.cc": "Cc", "addReceiver.notNeedAuth": "Real name authentication not required", "addReceiver.extracting": "Extracting", "addReceiver.autoFill": "Auto-fill the signatory", "addReceiver.failExtracting": "Failed to extract the signatory", "addReceiver.idNumberForVerifyErr": "Please input the correct identity document number", "addReceiver.noAccountErr": "Account cannot be empty", "addReceiver.ccError": "People who are copied can only fill in one cell phone number or email address.", "addReceiver.editorError": "补全人只能有一个接收手机或邮箱", "addReceiver.noUserNameErr": "Name cannot be empty", "addReceiver.noIDNumberErr": "Identity document number cannot be empty", "addReceiver.noEntNameErr": "Company name cannot be empty", "addReceiver.accountFormatErr": "Please enter the correct mobile phone number or email address.", "addReceiver.enterpriseNameErr": "Please enter the correct company name", "addReceiver.userNameFormatErr": "Please enter the correct name", "addReceiver.riskCues": "Warning", "addReceiver.riskCuesMsg": "If the signatory sign without real name authentication, you will need to provide your own evidence of the signatory's identity in the event of a dispute over the contract. To avoid this risk, please select \"Real name authentication required\" before sending.", "addReceiver.confirmBtnText": "Select \"real name authentication required\"", "addReceiver.cancelBtnText": "Select \"Real name authentication not required\"", "addReceiver.attachLengthErr": "You can only add up to 50 attachments for a single signatory.", "addReceiver.collapse": "Fold", "addReceiver.expand": "Collapse", "addReceiver.delete": "Delete", "addReceiver.saySomething": "Add Comments", "addReceiver.addImage": "Add document", "addReceiver.addImageTips": "Supports word, excel, pdf and images, and can be previewed online before signing. No more than {num} copies.", "addReceiver.addSourceFile": "Add source files", "addReceiver.addSourceFileTips": "Supports word、excel and pdf. Online preview is not available. You must download to view, but after download, the format is still word or excel or pdf so as to continue editing. No more than {num} copies.", "addReceiver.addFile": "add compressed file", "addReceiver.addFileTips": "Supports zip file, the hash value of the compressed file will be recorded in the contract's legal evidence. Limit size to {size} M, 1 copy only.", "addReceiver.addFileTipsApproval": "Supports zip file. Limit size to {size} M, 1 copy only.", "addReceiver.canDownload": "Allow to be downloaded", "addReceiver.shouEntry": "Display the entrance", "addReceiver.shouEntryTip": "After hiding, the sender will no longer see the entry to add new file when sending the contract using the template to avoid misoperation.", "addReceiver.itemRequire.1": "Option", "addReceiver.itemRequire.2": "Required", "addReceiver.uploadFile": "Upload file", "addReceiver.selectFile": "Select file", "addReceiver.confirm": "Confirm", "addReceiver.emptyFile": "No file can be selected, please upload the file.", "addReceiver.pleaseSelectOne": "Please select a file", "addReceiver.keepSourceFiles": "Keep the source files", "addReceiver.keepSourceTip": "Conversion to PDF to support preview by default. If you choose to keep the source file, the subsequent upload file will not be converted and the reviewer needs to download it to view the file.", "addReceiver.give": "to", "addReceiver.fileMax": "Exceed the limit of upload", "addReceiver.noReceivers": " No signing parties designated yet. All intended signatories, including the sender (your company/yourself), must be added as signing entities or individuals on this page.", "addReceiver.needStamp": "Company signature requirements: After setting company signature, signing companies {entName} must also set up signing roles for seal method.", "addReceiver.role": "Role：", "addReceiver.skip": "Got it", "addReceiver.toSetting": "Go to Settings", "addReceiver.signerLimit": "Your current version does not support more than {limit} relative signatories.", "addReceiverGuide.usageGuide": "Usage guide", "addReceiverGuide.guideTitle": "How to add a new signatory", "addReceiverGuide.receiverType": "You need to choose the way the signatory participates in the contract (one of six options).", "addReceiverGuide.asEntSign": "Signing on behalf of the company:", "addReceiverGuide.sealSub": "The signatory needs to stamp the contract with the company seal created in the Company console module.", "addReceiverGuide.signatureSub": "The legal person or senior executive signs the contract on behalf of the enterprise. The enterprise has the right to transfer the contract, making the signer unable to view the contract.", "addReceiverGuide.vipOnly": "Available in Advanced Edition", "addReceiverGuide.stampSub": "The signatory can stamp and put handwritten shgnature on the contract.", "addReceiverGuide.confirmSealSub": "Such as financial statements, confirmation letters and other documents. The signatory shall check before stamping.", "addReceiverGuide.asPersonSign": "Signed on behalf of an individual.", "addReceiverGuide.asPersonSignTip": "Individual sign, not on behalf of any company", "addReceiverGuide.asPersonSignDesc": "Private contracts of the signatory, such as loan contracts, employment and resignation, etc.", "addReceiverGuide.scanSign": "Signing by scaning the QR code", "addReceiverGuide.scanSignDesc": "No need to specify the signatory when start the contract. After the contract is created, anyone can scan the QR code/click the link to sign, which is applicable to some scenarios, such as logistics document signing in goods receiving scenario.", "addReceiverGuide.selectSignTypeTip": "Please select the way the signatory participates in the contract first", "addReceiverGuide.howToConfigSignType": "How to set the signing method?", "addReceiverGuide.entConfigSignType": "签约方为企业时，可设置以下方式：", "addReceiverGuide.personConfigSignType": "签约方为个人时，可设置以下方式：", "addReceiverGuide.howConfig": "How to config:", "addReceiverGuide.howConfigTip1": "1. Firstly, click \"+ Add signing company\" or \"+ Add signing individual\" on the \"Set signatory\" page.", "addReceiverGuide.howConfigTip2": "2. Click \"Stamp\" or \"Signature\" to select more methods.", "addReceiverGuide.signRoleNote": "Why setting the role of the signatory? ", "addReceiverGuide.baseFunction": "Basic ability:", "addReceiverGuide.baseUsage1": "It is more convenient to locate the role of the signatory in the contract. You can fill in the contact information and locate the signing location according to the role name.", "addReceiverGuide.baseUsage2": "Different signing roles use different seals, and a contract can specify multiple seals.", "addReceiverGuide.baseUsage3": "Switch contract payer by signing role", "addReceiverGuide.advanceFunction": "Advanced ability:", "addReceiverGuide.advanceUsage1": "Set the \"Quick Views\" according to the status of the signing role (such as read, not signed) to track the contract progress.", "addReceiverGuide.advanceUsage2": "The developer needs to use the signing role field to call the API.", "addReceiverGuide.advanceUsage3": "Advanced functions such as contract voiding require the signing role field.", "addReceiverGuide.advanceUsage4": "TThe seal image can be specified according to the signing role.", "addReceiverGuide.advanceUsage5": "You can set whether automatic signing is allowed according to the signing role. ", "addReceiverGuide.stepTip1": "After clicking on it, you can also adjust the way the signatory participates in the contract in the drop-down box.", "addReceiverGuide.stepTip2": "Please set the \"Signing Role\" of the signatory in the contract here.", "addReceiverGuide.stepTip3": "If you do not want to add the signatory, click here to remove it.", "templateReceiverConfig.err.setCurrentSender": "Please specify the current sender", "templateReceiverConfig.err.atLeastOneReceiver": "Add at least a signatory", "templateReceiverConfig.err.atLeastOneValidReceiver": "Add at least one available signatory", "templateReceiverConfig.err.atLeastOneZhReceiver": "Signatories not added to Chinese platforms", "templateReceiverConfig.err.atLeastOneJaReceiver": "Signatories not added to international platforms", "templateReceiverConfig.err.atLeastOneNecessary": "At least either the name of the operator or the operator's ID number shall be selected.", "templateReceiverConfig.err.signaturesCountLarge": "The number of the operator's identity verifications cannot be less than the number of corporate signatures.", "templateReceiverConfig.err.signaturesCountLess": "The number of the operator's identity verifications cannot be greater than the number of receiving accounts.", "templateReceiverConfig.err.batchMultiUseConflict": "When adding signatories in batches, multiple signatures cannot be used at the same time.", "templateReceiverConfig.err.idNumber": "Please input the correct identity document number.", "templateReceiverConfig.err.idNumberNotEmpty": "Identity document number cannot be empty", "templateReceiverConfig.err.idNumberWrong": "Please input the correct identity document number.", "templateReceiverConfig.err.accountNotEmpty": "Account cannot be empty.", "templateReceiverConfig.err.accountWrong": "Please enter the correct mobile phone number or email address.", "templateReceiverConfig.err.userNameNotEmpty": "Name cannot be empty.", "templateReceiverConfig.err.userNameWrong": "Please enter the correct name.", "templateReceiverConfig.err.receiverWrong": "Signing party information is not properly formatted. Please check the error messages (usually in red) on the page, make changes accordingly, and try again.", "templateReceiverConfig.err.atLeastOneSigner": "Please add at least one signatory.", "templateReceiverConfig.err.atLeastOtherOpertar": "跨境合同须设置跨平台的签约方", "templateReceiverConfig.err.attachNotEmpty": "Please fill in the name of the attachment.", "templateReceiverConfig.err.onlyOnProxyOrMultiAccounts": "Only one signing role in the same signing sequence can use the front desk collection or multi-account contract receiving function. Please choose ‘sign in order’ and set different signing sequences.", "templateReceiverConfig.err.saveEditWrong": "Incorrect account information, vacant field or Excel format error. Cannot proceed.", "templateReceiverConfig.err.roleNameNotEmpty": "Signing role cannot be empty.", "templateReceiverConfig.err.roleNameNotRepeat": "Signing role cannot be same.", "templateReceiverConfig.err.importSignerTip": "No signatory has been imported yet.", "templateReceiverConfig.err.importPicTip": "No images have been imported.", "templateReceiverConfig.err.importFail": "Import failed", "templateReceiverConfig.err.errorInfoNote": "（Please drag the scroll bar in the table below to see the error reporting instructions marked in red.）", "templateReceiverConfig.err.lost": "{name} is missing;", "templateReceiverConfig.err.ddlTooLittle": "Valid timeframe of the signature cannot be less than 15 minutes.", "templateReceiverConfig.err.encryptionSignPasswordLimit": "加密签署码为4-8位数字", "templateReceiverConfig.tip.moreThanTipPre": "页面仅展示Excel表中前500条，更多合同格式检查结果需", "templateReceiverConfig.tip.download": "下载文件", "templateReceiverConfig.tip.moreThanTipEnd": "至本地查看", "batchImport.optTip": "and upload it directly after filling in the template (see template sheet2 and sheet3 for the uploaded file name, and copy and fill in the template).", "batchImport.optDecTip": "Upload a compressed file package. The pictures and other attachments that need to be added in the contract shall be uploaded as compressed packages.", "batchImport.excelTemplate": "Excel template", "batchImport.downloadExcelTemplate": "Download Excel template  ", "batchImport.contentFileImportSuccess": "The following content of documents and signatories have been imported successfully.", "batchImport.batchImport": "Bulk import", "batchImport.reBatchImport": "Re-import in bulk", "batchImport.batchImportTip": "Batch import tip", "batchImport.iKnow": "I see", "batchImport.longTimeLoadingTip": "There are a lot of contracts and they are being analyzed. Please wait patiently.", "batchImport.zipImport": "Upload Compressed File Package", "batchImport.zipReimport": "Re-upload Compressed File Package", "batchImport.importView": "Import to view", "batchImport.documentsImport": "Upload compressed document package", "batchImport.documentsReimport": "Re-upload compressed document package", "batchImport.msg.success": " Imported successfully", "batchImport.msg.fail": "Import failed", "batchImport.msg.confirm": "OK", "batchImport.msg.templateDisabled": "This template cannot be used", "batchImport.msg.authRestored": "Your permission to use this template has been withdrawn by the template creator.Your permission to use this template has been withdrawn by the template creator", "batchImport.msg.hasDel": "This template has been deleted by the creator", "batchImport.msg.listBack": "Return to the template list", "batchImport.selectedBlank": "Document Upload Instructions", "batchImport.selectedBlankTip2Title": "2. Click the \"Upload Document\" button", "batchImport.selectedBlankTip2Content": "Ensure that the uploaded file is applicable to all contracting parties (i.e., all parties see the same contract content).", "batchImport.selectedBlankTip1Title": "1. Click the \"Upload Document Zip\" button", "batchImport.selectedBlankTip1Content": "If different contracting parties need to view different files, please package these files into a compressed file. Click the \"Upload Document Zip\" button and select the compressed package to upload.", "batchImport.checkBlankDocsTitle": "Check the documents that need to be uploaded as a compressed package.", "batchImport.selectedBlankMsg": "Please select the blank document first", "batchImport.confirm": "Confirm", "batchImport.blankDecTip": "Upload a compressed document package (max 50MB). The contract documents that need to be added to the contract are uploaded as compressed packages.", "batchImport.step": "STEP {step}:", "receiverItem.setNoticelang": "Notification Language", "receiverItem.limitFaceConfigTip": "This feature is unavailable due to your contract price being too low. Please contact BestSign for consultation", "receiverItem.mutexMsg": "the “{msg}” has been set, Please delete its setting first and then select again", "receiverItem.batchImported": "Imported in bulk", "receiverItem.batchNotImported": "Pending for bulk import", "receiverItem.batchCheckbox": "Bulk add", "receiverItem.importedNum": "Successfully imported {batchNum} signatories", "receiverItem.checkTooltip": "View the effect schematic", "receiverItem.authDropdownTooltip": "Verification of the identity of the signatory", "receiverItem.signTypeTooltip": "Contract participating methods", "receiverItem.moreConfigTooltip": "Set more signing details", "receiverItem.caseDlgTitle": "Check the effect schematics for bulimport.", "receiverItem.clickView": "Click to view", "receiverItem.entName": "Company Name", "receiverItem.entNameTips": "Tip: Signing is only allowed when the signatory's company name is exactly the same to the name designated by the sender", "receiverItem.userName": "Handler", "receiverItem.userNamePlace": "Full name (optional)", "receiverItem.userAccount": "Mobile / Email for signatory", "receiverItem.userAccountJa": "Email for signatory", "receiverItem.userAccountDemand": "(Up to 10; can be separated by semicolons)", "receiverItem.proxy": "Reception desk receiving", "receiverItem.addressBookTooltip": "Contact address book", "receiverItem.proxyTips": "If you don't know the signatory's account or the signatory hasn't had an account yet, please choose \"received by reception desk\" mode", "receiverItem.dai": "On behalf of", "receiverItem.name": "Full name", "receiverItem.IDCard": "Identity document number", "receiverItem.IDCardPlace": "For identity verification before signing", "receiverItem.addressBook": "Contact address book", "receiverItem.role": "Signing role", "receiverItem.rolePlace": "Such as employees/distributors", "receiverItem.roleTooltip": "To distinguish different signatories.", "receiverItem.byAddressBook": "Brought by your contact address book", "receiverItem.error.userAccountLessThan": "Mobile phone number / Email address receiving notification must not exceed {num}", "receiverItem.error.userAccountNotRepeat": "Mobile numbers/Emails for reception must not repeat", "receiverItem.error.entNameLessThan": "Company name cannot exceed {num} characters", "receiverItem.error.signerInContract": "The signatory is added to the contract signing", "receiverItem.error.signerNotInContract": "The signatory is not included in the contract signing", "receiverItem.invite": "Invite your partners", "receiverItem.addFromBook": "Select from Address Book", "receiverItem.userNameToolTip.0": "The name of the signatory is only used for your future management and statistics. It will not be used to check whether the name of the signatory is the same.", "receiverItem.userNameToolTip.1": "For verification, you need to enable the function of \"Verify the identity of the operator\".", "receiverItemHeader.contractDownloadControl": "Contract download code", "receiverItemHeader.signerPerson": "Individual signatory", "receiverItemHeader.signerEnt": "Company signatory", "receiverItemHeader.needAuth": "Real name authentication required", "receiverItemHeader.notNeedAuth": "Real name authentication not required", "receiverItemHeader.needAuthEnt": "Handler individual real name authentication required", "receiverItemHeader.notNeedAuthEnt": "Handler individual real name authentication not required", "receiverItemHeader.sign": "Signature", "receiverItemHeader.entSign": "Company signature", "receiverItemHeader.stamp": "Stamp", "receiverItemHeader.stampSign": "Stamp and signature", "receiverItemHeader.requestSeal": "Stamp for Business Check", "receiverItemHeader.cc": "Cc", "receiverItemHeader.editor": "Editor", "receiverItemHeader.scanSign": "Sign after scan verify code", "receiverItemHeader.signCheck": "Signing verification", "receiverItemHeader.messageAndFaceVerify": "Face scan + verification code ", "receiverItemHeader.faceFirst": "Priority sign with facial verification, alternate sign with SMS code verification", "receiverItemHeader.faceMust": "Must sign with facial verification", "receiverItemHeader.noHand": "Handwritten signature not allowed", "receiverItemHeader.mustHand": "Handwritten signature required", "receiverItemHeader.notify": "Signing Instructions", "receiverItemHeader.handwritingRec": "Handwriting recognition", "receiverItemHeader.readAll": "Must finish reading before sign", "receiverItemHeader.dataCollect": "Information collection", "receiverItemHeader.attachDoc": "Contract attachments", "receiverItemHeader.attachDocTip": "(10M limited per file)", "receiverItemHeader.mainDoc": "Signatory information", "receiverItemHeader.attachDocTips": "The submitted information is used to help you track the status of contract performance and determine whether business is being performed properly. Once set up, this signatory must submit as required", "receiverItemHeader.mainDocTips": "The submitted information is used to help you check the qualifications of the signatories and determine whether you can start or continue business with them. If the same information has already been submitted by the signatory, you may not submit it again.", "receiverItemHeader.scanSignTip": "After the contract is issued, anyone can scan the code to sign. This is applicable to the logistics document receiving scenario.", "receiverItemHeader.other": "Others", "receiverItemHeader.notifyOff": "Turn off SMS/email notifications", "receiverItemHeader.notifyOffJa": "Turn off email notifications", "receiverItemHeader.notifyForeign": "Switch notification language", "receiverItemHeader.notifyForeignTips": "Notifications in foreign languages are not supported via SMS, only via email", "receiverItemHeader.signerPay": "The signer pay", "receiverItemHeader.signerPayDesc": "This function does not support automatic signature, paper signature scenario for now.", "receiverItemHeader.more": "More", "receiverItemHeader.ddl": "Signing time limit", "receiverItemHeader.encryptionSign": "Use Encrypted Signing", "receiverItemHeader.signerAuthCheck": "Verification of the handler's identity", "receiverItemHeader.twoFactorAuthentication": "开启二要素认证", "sendedEdit.editorNameTip": "与发件方企业保持一致", "sendedEdit.completeDescription.0": "Some content in the contract can be left blank and then completed via the current account after the contract is sent. Those who fill in the missing information the contract shall not stamp or sign the contract. The contract must be signed or stamped by a member of the sender's company. This practice applies to business scenarios involving a large number of participants in contract signing.", "sendedEdit.completeDescription.1": "查看设置方法", "sendedEdit.completeAttachment": "Completion of attachments: Only the first recipient of the contract can complete contract information, and the template shall be configured with the contract attachment field in advance.", "sendedEdit.completeOther": "Completion of other content: Check the content to be completed, such as the contracting instructions, contract content fields, and the account number of the signing individual. View setup method.", "sendedEdit.completeMustSignOrderTip": "请先勾选“顺序签署”，且合同补全执行人的顺序不能是最后一位", "sendedEdit.sendedEditMustSignByOrder": "当前存在补全角色，补全功能要求必须启用“顺序签署”", "sendedEdit.name": "发送后补全签署人", "sendedEdit.sendedEditDesc": "发件人在发送合同时可以不填写该签约角色的账号信息，如果填写了账号信息，也需由补全合同信息的执行人账号确认后才生效。", "sendedEdit.howToPrepare": "设置方法", "sendedEdit.prepareContentTip": "签约角色A作为合同补全执行人不需要补齐任何字段或账号，不符合“发出合同再补全”功能的用法。您可以直接删除签约角色A。", "sendedEdit.configGuideTip": "可在合同上预留合同内容字段或签约个人的账号，在合同发出后再由签约角色{role}补全。", "sendedEdit.viewConfigGuide": "查看设置方法", "sendedEdit.prepareLabels": "合同预留内容字段：", "sendedEdit.prepareLabelsDesc": "编辑模板时，将内容填写人设置为发件人，并选择由哪位签约角色补全。", "sendedEdit.prepareRoles": "合同预留签约个人：", "sendedEdit.prepareSignerDesc": "1. 在设置签约方页面，添加签约个人后勾选发送后补全签署人。", "sendedEdit.prepareCompletorDesc": "2. 选择该签约方信息由哪位签约角色补全。", "sendedEdit.batchImportConflictEditorTip": "执行补全操作的签约角色不支持批量添加。将其切换为“盖章”等其他合同参与方式后才可勾选“使用时可批量添加”", "sendedEdit.batchImportConflictEmptyRoleTip": "待发送后补全的账号不支持批量添加。取消“发送后补全签署人”后，才可勾选“使用时可批量添加”", "sendedEdit.editorConfigFirstTip": "在待补全信息的签约个人签署顺序前，需存在一个补全合同的执行人", "sendedEdit.senderFill": "发送时填写", "sendedEdit.fillByRole": "由签约角色\"{role}\"填写", "sendedEdit.editorCompleteInfo": "请补全以下信息", "sendedEdit.completeBy": "由签约角色", "sendedEdit.complete": "补全", "receiverItemExtends.twoFactorAuthenticationTips": "该签署方需要完成二要素认证才能签署", "receiverItemExtends.encryptionSignTips": "Use Encrypted Signing（The current signer must enter this password to complete the contract signing.）", "receiverItemExtends.encryptionSignCode": "Encrypted Signing Code", "receiverItemExtends.pleaseInput": "Enter 4-8 numeric digits", "receiverItemExtends.contractDownloadControl": "Enable download code", "receiverItemExtends.contractDownloadControlTips": "The signatory needs to fill in the download code when downloading the contract, and the sender can view the download code in the contract details page.", "receiverItemExtends.messageAndFaceVerify": "Facial verification + verification code ", "receiverItemExtends.messageAndFaceVerifyTips": "The user needs to complete the face scan and verification code before signing the contract. The user needs to complete real name authentication before face scan. These are only applicable to mainland residents.", "receiverItemExtends.faceFirst": "Facial verification as the first choice and signing with verification code as an alternative method.", "receiverItemExtends.faceFirstTips": "The system will first use face scan for authenticationn upon signing and the authentication code will be used instead if face scan fails.", "receiverItemExtends.faceMust": "Sign with facial verification", "receiverItemExtends.faceMustTips": "The signatory needs to finish facial verification to complete signing", "receiverItemExtends.faceApplicable": "适用人群", "receiverItemExtends.faceMustTooltip.1": "刷脸签署只支持：", "receiverItemExtends.faceMustTooltip.2": "1、大陆居民（身份证）", "receiverItemExtends.faceMustTooltip.3": "2、持有港澳居民通行证的港澳居民", "receiverItemExtends.faceMustTooltip.4": "3、持有外国人永久居留身份证的外国居民", "receiverItemExtends.faceMustTooltip.5": "4、定居国外的中国公民（使用国内护照）", "receiverItemExtends.faceMustTooltip.6": "其他用户暂不支持，将默认进行验证码校验签署。", "receiverItemExtends.faceMustTooltip.7": "上述1证件以外的刷脸方式只支持微信H5刷脸，", "receiverItemExtends.faceMustTooltip.8": "如您配置了必须支付宝刷脸，则2、3、4证件用户将默认验证码校验签署。", "receiverItemExtends.noHand": "Handwritten signature is not allowed", "receiverItemExtends.noHandTips": "The user can only choose from signatures that are already set up or use the default fonts to complete signing", "receiverItemExtends.mustHand": "Handwritten signature required", "receiverItemExtends.mustHandTips": "Handwritten signature is required for this signatory to complete signing", "receiverItemExtends.useScanCodeClaim": "Enable scanning code to claim", "receiverItemExtends.scanCodeClaimTip": "For the front desk collection contract, after checking, the contracting party can only claim the contract by scanning the inspection code, and cannot claim the contract by other methods. The inspection code can be downloaded through the inspection code interface or on the contract details page after the contract is sent, and then notified to the corresponding claimant. (The receiver can see the inspection code on the contract details or contract content page and scan the code to complete the claim).", "receiverItemExtends.notifyLabel.1": "Signing Instructions", "receiverItemExtends.notifyLabel.2": "(255 characters only)", "receiverItemExtends.notifyLabel.3": "Send and then complete the signing instructions.", "receiverItemExtends.handwritingRec": "Handwriting recognition", "receiverItemExtends.handwritingRecTips": "The handwritten name of this user will be compared with the name specified by the sender or in the real-name authentication to ensure consistency", "receiverItemExtends.readAll": "Read before signing", "receiverItemExtends.readAllTips": "Read before signing", "receiverItemExtends.attachDoc": "Add contract attachments", "receiverItemExtends.attachDocTip": "（Each uploaded file can not exceed 10M）", "receiverItemExtends.mainDoc": "Add signatory information", "receiverItemExtends.notifyOff": "Turn off SMS notification", "receiverItemExtends.notifyOffTips": "When enabled, this contracting party will not receive  notifications on contracting (which will be sent by default when disabled)", "receiverItemExtends.notifyForeign": "Switch notification language", "receiverItemExtends.notifyForeignTips": "Send foreign notifications to all contracting parties,", "receiverItemExtends.English": "English", "receiverItemExtends.Japanese": "Japanese", "receiverItemExtends.Chinese": "Chinese", "receiverItemExtends.Arabic": "Arabic", "receiverItemExtends.signerPay": "The signer pay", "receiverItemExtends.signerPayTip": "This contract is paid by this recipient", "receiverItemExtends.dataName": "Attachment name", "receiverItemExtends.remarks": "Remark", "receiverItemExtends.itemRequire.1": "Option", "receiverItemExtends.itemRequire.2": "Required", "receiverItemExtends.addressLine": "Details of the address collected (required)", "receiverItemExtends.require": "Required", "receiverItemExtends.notRequire": "Optional", "receiverItemExtends.addressCheckbox.province": "Province", "receiverItemExtends.addressCheckbox.city": "City", "receiverItemExtends.addressCheckbox.area": "District", "receiverItemExtends.addressCheckbox.detail": "Detailed address (such as street, door number, etc.)", "receiverItemExtends.storeTypeList.0": "Text", "receiverItemExtends.storeTypeList.1": "Picture", "receiverItemExtends.storeTypeList.2": "Information with single option", "receiverItemExtends.storeTypeList.3": "Information with multiple options", "receiverItemExtends.storeTypeList.4": "PDF file", "receiverItemExtends.storeTypeList.5": "Date information", "receiverItemExtends.storeTypeList.6": "Address", "receiverItemExtends.storeTypeList.10": "表单资料", "receiverItemExtends.boxContent.dataBox": "File cabinet", "receiverItemExtends.boxContent.basicDataCollection": "Basic information collection", "receiverItemExtends.boxContent.customDataCollection": "Customized data collection", "receiverItemExtends.boxContent.personalRealName": "Individual real name authentication", "receiverItemExtends.boxContent.selectRequire": "(Required)", "receiverItemExtends.boxContent.applyForAuthorization": "Apply for authorization", "receiverItemExtends.boxContent.entAuthorizationList.entAuth": "Company real name authentication", "receiverItemExtends.boxContent.entAuthorizationList.seal": "Company stamp (you can sign using other company's stamp under authorization)", "receiverItemExtends.boxContent.entAuthorizationList.send": "Entrusted sending (you are entrusted to send contracts for other companies)", "receiverItemExtends.notify.title": "Don't send SMS/email notification to the following signatory (notificaiton will be sent by default when this switch is off)", "receiverItemExtends.notify.explain.1": "No notification for the fixed signatories. The variable signatories will receice the notification.", "receiverItemExtends.notify.explain.2": "Don't send to all signatories.", "receiverItemExtends.attach.dataName": "Attachment name", "receiverItemExtends.attach.dataType": "Document type", "receiverItemExtends.attach.imgFile.name": "Image data", "receiverItemExtends.attach.imgFile.support": "Support png, jpg, jpeg files", "receiverItemExtends.attach.imgFile.eg": "Example: identity ID card photo", "receiverItemExtends.attach.imgFile.holderText": "Example: Please upload your identity ID photo (optional)", "receiverItemExtends.attach.docFile.name": "File information", "receiverItemExtends.attach.docFile.support": "Support pdf, excel, word, txt, zip, and xml files", "receiverItemExtends.attach.docFile.eg": "Example: Medical examination report", "receiverItemExtends.attach.docFile.holderText": "Example: Please upload the medical examination report (optional)", "receiverItemExtends.attach.remake": "Remark", "receiverItemExtends.attach.addData": "Add attachments", "receiverItemExtends.attach.error.dataNotEmpty": "Attachment name cannot be empty", "receiverItemExtends.attach.error.attachNameNotSame": "The name of attachment cannot be the same to other attachment's.", "receiverItemExtends.attach.collapse": "Fold", "receiverItemExtends.attach.expand": "Collapse", "receiverItemExtends.attach.delete": "Delete", "receiverItemExtends.auth.new": "Newly added", "receiverItemExtends.auth.newLimit": "(up to 5)", "receiverItemExtends.auth.field.name": "Handler name", "receiverItemExtends.auth.field.id": "Identity document number", "receiverItemExtends.auth.placeholder.name": "Please enter the real name", "receiverItemExtends.auth.placeholder.id": "Please enter the identity document number", "receiverItemExtends.auth.placeholder.nameRequire": "Same name can be signed (required)", "receiverItemExtends.auth.placeholder.idRequire": "Only if the ID card is consistent can be signed (required)", "receiverItemExtends.auth.placeholder.nameRule": "Signing on behalf of the company is only allowed when the name of the handler consistent with the name on the handler's identity document.", "receiverItemExtends.auth.tips.entSignature": "This operator only needs to use personal signature (corresponding to personal CA certificate) to complete the signature. Seal is not required. However, it is still necessary to designate an additional person to seal on behalf of the company.", "receiverItemExtends.auth.tips.stampSignature": "When using \"Stamp and signature\" function, the handler also needs to add individual signature to complete the signing. Individual real name authentication is required before signing.", "receiverItemExtends.auth.checkboxLabel.onlyStamp": "Enable verification of the handler's identity. The handler has to finish individual real name authentication before signing.", "receiverItemExtends.auth.checkboxLabel.onlyStampTip": "If name and identity document information are not filled in, only the real name of the handler is required, and the identity of the handler does not need to be verified. ", "receiverItemExtends.auth.checkboxLabel.withSignature": "Enable verification of the handler's identity. Whether enable or not, the handler still needs to finish individual real name authentication before signing.", "receiverItemExtends.auth.checkboxLabel.useMulti": "Enable multiple signatures for the company.", "receiverItemExtends.auth.checkboxLabel.sigNum.0": "Set up for corporate signatures", "receiverItemExtends.auth.checkboxLabel.sigNum.1": "Signing position at", "receiverItemExtends.auth.checkboxLabel.showHideTip": "When hidden, the sender will no longer see this configuration item when using a template to send contracts so as to avoid misoperation.", "receiverItemExtends.auth.checkboxLabel.showThis": "Show this line", "receiverItemExtends.auth.checkboxLabel.hideThis": "Hide this line", "receiverItemExtends.auth.error.atLeast": "Either the name of the manager or the manager's ID number shall be selected.", "receiverItemExtends.ddl": "Valid timeframe of the signature", "receiverItemExtends.ddlDesc.0": "After receiving the contract, the contract must be signed in", "receiverItemExtends.ddlDesc.1": " days ", "receiverItemExtends.ddlDesc.2": " hours ", "receiverItemExtends.ddlDesc.3": " minutes. If not, the contract status will be changed to \"Overdue\". For signatories who have enabled the valid timeframe of the signature, the system will no longer automatically push a reminder that the contract is about to be signed.", "receiverItemExtends.scanSign.tip": "发合同时不需要写签署人，发出后任何人扫码/点查验页链接都可签署，适用物流单据收货场景", "receiverItemExtends.scanSign.notValidateSignVerificationCode": "只在登录时校验验证码，无需签署校验", "receiverItemExtends.authCheckMove.tip": "注意：原先此处\"经办人身份核验\"功能已移动至右侧更多菜单中，请在更多菜单中进行操作。", "contractInfo.attachmentInfo": "Attached document information", "contractInfo.contractDescInfo": "Contract description ", "contractInfo.batchImportLabelsTip": "No need to fill the content of the following fields in the excel table. The current page already has the content", "contractInfo.contractName": "Contract title", "contractInfo.fieldConfig": "Field configuration", "contractInfo.fieldConfigDesp": "Set the hidden display of the field", "contractInfo.fieldConfigTooltip": "This field configuration must be synchronized with the field configuration of the first document. If you want to modify it separately, please turn off the \"Sync to other documents in template\" of the first document. As shown in the figure:", "contractInfo.confirmTitle": "Tips", "contractInfo.fieldConfigConfirmTip": "The contract description field of each document in this template will be updated by the first document. Are you sure to enable field synchronization?", "contractInfo.contractNameRequire": "Please enter the contract title", "contractInfo.contractNameTooltip": "Please do not include special characters in the contract title, and the length should not exceed 100 characters.", "contractInfo.customNumber": "Internal business number", "contractInfo.contractType": "Contract type", "contractInfo.contractTypeIdsForApprove": "Contract type (Alternative)", "contractInfo.signValidateTerm": "Signing expiration date (days)", "contractInfo.signDeadLine": "Signing deadline", "contractInfo.signDeadLineTooltip": "If the contract has not been signed by this date, signing cannot be continued.", "contractInfo.selectDate": "Select a date", "contractInfo.contractExpireDate": "Contract expiration date", "contractInfo.contractExpireDays": "Period of contract validity (days)", "contractInfo.expireDateTooltip": "The expiration date in the contract is for the convenience of your contract management", "contractInfo.notNecessary": "Optional", "contractInfo.dateTips": "The expiration date of the contract is automatically identified for you. Please confirm.", "contractInfo.contractTitleErr": "Please do not include special characters in the contract name.", "contractInfo.inputFieldTip": "Please fill in the field.", "contractInfo.signExpireDaysLimitTip": "The contract validity period is only supported within the set {limit} days. ", "contractInfo.lockMustHasValue": "The current field content needs to be filled in before it can be locked", "contractInfo.lockOpenTip": "Click the button, the current field content will be locked, and it is forbidden to modify the field content when using the template.", "contractInfo.lockLimitTip": "The template administrator has forbidden the content of this field to be modified", "contractInfo.excelImportTip": "The following fields' content is to be imported from the Excel form.", "contractInfo.contractContentInfo": "Contract content field", "contractInfo.contractContentPicture": "Contract decoration picture", "contractInfo.contractContentAttachment": "Attachments to the contract", "contractInfo.justImg": "Upload can only be in picture format", "contractInfo.lessThan5": "The size of the uploaded image cannot exceed 5MB.", "contractInfo.importSuccess": "Imported successfully", "contractInfo.clickToView": "Click to view", "contractInfo.hasImported": "Imported in bulk", "contractInfo.readyToImport": "Pending for bulk import", "contractInfo.noticeContractName": "Multi-document contract title", "contractInfo.noticeContractNameTooltip1": "This name shall be used as the contract name in all related notifications in the signing process including signing reminders, reviews, approvals, copies, contract completions, etc.", "contractInfo.noticeContractNameTooltip2": "On the contracort management page, use this name for multi-document contract, and use the 'contract title' of the documents as the independent name of the subcontract.", "contractInfo.msgExample": "Example (SMS):", "contractInfo.noticeContractNameExample1": "... Ltd. has sent ' to your account *** 《", "contractInfo.noticeContractNameExample2": "》，The deadline for signing ' is ...", "contractInfo.example": "Example", "contractInfo.view": "View", "contractInfo.replace": "Replace", "contractInfo.delete": "Delete", "contractInfo.attachmentType": "Please upload in Pdf, Word, Excel and image formats", "contractInfo.attachmentUploadError": "Upload failed", "contractInfo.attachmentDocumentTip": "The file is in {type} format. You need to download it and view locally. Proceed to download? ", "configTemplate.contractListAbove30": "The total number of contract documents cannot exceed 30", "configTemplate.crossPlatformContract": "Cross-platform contract", "configTemplate.config": "Set the template", "configTemplate.use": "Use the template", "configTemplate.save": "Save the template", "configTemplate.justSave": "Save", "configTemplate.nextStep": "Next", "configTemplate.uploadContract": "Upload your files", "configTemplate.confirmContract": "Verify the document", "configTemplate.configContract": "Add documents", "configTemplate.prepareReceiver": "Pre-set signatory", "configTemplate.configReceiver": "Add recipients", "configTemplate.pointPosition": "Specify signing locations", "configTemplate.batchSend": "Use templates to bulk send contracts", "configTemplate.batchImportInfo": "Bulk import contract and signatory information", "configTemplate.templateName": "Name of the template:", "configTemplate.templateNameRequired": "Please enter the name of the contract template.", "configTemplate.templateNameFormatTip": "Template name cannot contain square brackets.", "configTemplate.templateNote": "Remarks:", "configTemplate.addContract": "Add document", "configTemplate.contractInfoError": "Contract basic information incorrect. Please check and submit again.", "configTemplate.uploadContractTip": "Please upload the document.", "configTemplate.existEmptyContractTip": "There is a blank document. Please upload the content.", "configTemplate.accountPlaceholder": "Please enter the mobile phone number or email address.", "configTemplate.accountPlaceholderJa": "Please enter the email address.", "configTemplate.noSendContract": "The user has no permission to send contracts.", "configTemplate.defaultTemplateName": "Void Statement", "configTemplate.selectApproval": "Template Approval Flow", "configTemplate.selectedApprover": "Template Approver", "configTemplate.customizedApprove": "Custom Approval Flow", "configTemplate.configSenderField.toContractFill": "Go to the contract to fill in", "configTemplate.configSenderField.pageName": "Fields to be filled", "configTemplate.configSenderField.backTip": "Data won't be saved through direct return. Proceed to return? ", "configTemplate.configSenderField.fieldValidateTip": "Field information verification failed. Please confirm.", "field.approval": "Approval before sending", "field.send": "Send", "field.contractDispatchApply": "Request to send contract", "field.contractNeedYouSign": "You need to sign this document.", "field.ifSignRightNow": "Sign now?", "field.signRightNow": "Yes", "field.signLater": "Later", "field.signaturePositionErr": "Please specify the signature/stamp location for each signatory", "field.sendSucceed": "Send successfully", "field.confirm": "OK", "field.cancel": "Cancel", "field.qrCodeTips": "After signing, you can scan the QR code to see the details of the contract and check whether the contract has been tampered.", "field.pagesField": "Page {currentPage} of {totalPages}", "field.suitableWidth": "Suitable width", "field.signCheck": "Signature verification", "field.locateSignaturePosition": "Locate the signature / stamp", "field.append": "Add", "field.privateLetter": "Signing instructions", "field.signNeedKnow": "Signing instructions", "field.maximum5M": "Please upload a document less than 5M", "field.maximumSize": "Please upload a document smaller than {size}M", "field.uploadServerFailure": "Upload to server failed", "field.uploadFailure": "Upload failed", "field.uploadRepeatFileTip": "Do not upload the same file repeatedly", "localCommon.download": "Download", "localCommon.cancel": "Cancel", "localCommon.confirm": "Confirm", "localCommon.toSelect": "Please select", "localCommon.seal": "Stamp", "localCommon.signature": "Signature", "localCommon.signDate": "Date Signed", "localCommon.text": "Text", "localCommon.date": "Date", "localCommon.datetime": "Time", "localCommon.qrCode": "QR Code", "localCommon.number": "Number", "localCommon.dynamicTable": "Dynamic form", "localCommon.terms": "Terms and conditions of the contract", "localCommon.checkBox": "Checkbox", "localCommon.radioBox": "Radio", "localCommon.image": "Image", "localCommon.confirmSeal": "Confirmation stamp", "localCommon.tip": "Tips", "localCommon.confirmRemark": "Remarks regarding seals that do not meet requirements", "localCommon.optional": "Optional", "localCommon.require": "Required", "localCommon.comboBox": "DropDown", "localCommon.yes": "yes", "localCommon.no": "no", "labels.signTime": "Date Signed", "labels.optionLimitTip": "The maximum number of selectors has been reached", "labels.pageLimitTip": "It exceeds the page boundary and cannot be added", "labels.optionName": "Alternate option {count}", "labels.singerName": "Recipient", "labels.fdaDate": "Time of signing", "labels.fdaReason": "To be selected", "labels.sealArea": "Seal here", "labels.designateSeal": "Go to Specified Stamp", "labels.cancelSeal": "Cancel", "labels.changeSeal": "Switch Stamp", "labels.selectableSeal": "Selectable Stamps", "labels.noSealList": "The signer does not have an available stamp. A stamp must be assigned to them by the administrator before one can be designated.", "customLabelEdit.labelName": "Name", "customLabelEdit.require": "Required", "customLabelEdit.format": "Format", "customLabelEdit.equalWidth": "List equal width", "customLabelEdit.adjustWidth": "Adaptive width", "customLabelEdit.contentFiller": "Content filled by", "customLabelEdit.senderOnly": "Only the sender can fill in this content.", "customLabelEdit.sender": "Sender", "customLabelEdit.senderTip": "If \"Sender\" is selected, the content of the field shall be filled in by the sender before the contract is sent.", "customLabelEdit.signer": "Signatory", "customLabelEdit.signerTip": "If you select \"Signatory\", the content of this field shall be filled in when the signatory signs the contract.", "customLabelEdit.signatureSize": "Signature size", "customLabelEdit.default": "<PERSON><PERSON><PERSON>", "customLabelEdit.custom": "Custom", "customLabelEdit.labelSize": "Display size", "customLabelEdit.labelWidth": "<PERSON><PERSON><PERSON>", "customLabelEdit.labelWidthPlaceHolder": "Please enter the width", "customLabelEdit.labelHeight": "Height", "customLabelEdit.labelHeightPlaceHolder": "Please enter the height", "customLabelEdit.labelChangeAllPageTip": "Position move can be applied to all page numbers. Please check the effect of change on other page numbers.", "customLabelEdit.autoSystemFill": "Automatically filled in by the system.", "customLabelEdit.alternativeItem": "Alternate option", "customLabelEdit.dateFormat": "Format", "customLabelEdit.labelFontSize": "Font size", "customLabelEdit.labelFontSizePlaceHolder": "Please choose the font size", "customLabelEdit.labelAlign": "Alignment", "customLabelEdit.labelDescribe": "Instructions for filling in", "customLabelEdit.labelDescribeTip": "Optional, no more than 20 characters", "customLabelEdit.labelRequire": "Filling requirement", "customLabelEdit.labelRequireTip": "Required", "customLabelEdit.labelFillAllRegion": "The image fills the entire placeholder area", "customLabelEdit.labelFillAllRegionTip": "Automatically adapt according to the size of the image area to fill the entire image area", "customLabelEdit.confirm": "OK", "customLabelEdit.cancel": "Cancel", "customLabelEdit.defaultValue": "Set default values", "customLabelEdit.selectDateDefaultValue": "Select date", "customLabelEdit.messageTip.nameError": "Please enter the name.", "customLabelEdit.messageTip.itemError": "Please enter the alternate option.", "customLabelEdit.messageTip.itemSameError": "Options cannot be the same.", "customLabelEdit.messageTip.itemRegError": "Option names can only be a combination of Chinese, English, and numbers.", "customLabelEdit.messageTip.widthError": "Please enter a width greater than or equal to 28.", "customLabelEdit.messageTip.widthMaxError": "Please enter a width value less than {width}. ", "customLabelEdit.messageTip.heightError": "Please enter a height value greater than or equal to 20.", "customLabelEdit.messageTip.heightMaxError": "Please enter a height value less than {height}.", "customLabelEdit.messageTip.markOptionValueTip": "Option name cannot contain special characters /#@()", "customLabelEdit.messageTip.hasEnComma": "Option names cannot include English commas", "customLabelEdit.messageTip.cannotHasEnComma": "The name of the optional item cannot contain English commas.You need to re-edit the template and remove the English commas", "customLabelEdit.messageTip.wordNumError": "Please fill in the number of characters in the field content", "customLabelEdit.messageTip.overCountLimit": "Support up to 500 options", "customLabelEdit.messageTip.numberDefaultError": "Wrong field format", "customLabelEdit.defaultValueTip": "Check the selection box to set the default value.", "customLabelEdit.addOption": "Add Option", "customLabelEdit.batchAddOption": "Add options in bulk", "customLabelEdit.selectTermType": "Please select the clause category", "customLabelEdit.wordNum": "Content word count", "customLabelEdit.wordNumTip": "Used to calculate the reserved width of the field content, with no limit to the number of words in the field content, with 5 by default. Any part exceeding the interface will be cut.", "customLabelEdit.location": "Coordinate position", "customLabelEdit.xLocation": "From the left", "customLabelEdit.yLocation": "From the top", "customLabelEdit.integer": "Integer", "customLabelEdit.decimalLimit": "Limit", "customLabelEdit.decimal": " decimals", "customLabelEdit.numberFormat": "Format", "customLabelEdit.formatValid": "Format Verification", "customLabelEdit.noFormat": "No format verification", "customLabelEdit.idCard": "Mainland China Resident ID Card", "customLabelEdit.phoneNumber": "11-digit mobile phone number", "customLabelEdit.formatValidTip": "If the content entered in the fields does not meet the format requirements, the signer will not be able to submit.", "customLabelEdit.labelDescribeTooltip": "When signing a contract, you can view the preset text in the \"Filling Instructions\" to ensure that the signer accurately understands the content to be filled in.", "labelEdit.ridingSealSetTip": "The setting applies to all cross-page seals", "labelEdit.ridingConfig": "A cross-page seal is required on each page. Single-sided printing is advised.The number of pages of the document should be greater than or equal to 2, otherwise the seal across pages will not be displayed after signing. | A cross-page seal is required for only odd pages. Double-sided printing is advised.The number of pages of the document should be greater than or equal to 3, otherwise the seal across pages will not be displayed after signing.", "labelEdit.advancedSettings": "Advanced Settings", "labelEdit.receiver": "Recipient", "labelEdit.info.0": "Please note:", "labelEdit.info.1": "1）If the same business field is added in multiple locations in the template,  you only need to fill in its content once.", "labelEdit.info.2": "2）The settings will synchronously update to all business fields with the same name within the current template.", "labelEdit.info.3": "Note: The signer only needs to fill in if no suitable seals are selected.", "labelEdit.wordbillLabel.selectType": "Select field type", "labelEdit.sealSyncPosition.title": "Seal Sync Position", "labelEdit.sealSyncPosition.description": "Description", "labelEdit.sealSyncPosition.closeTip": "Are you sure to turn off \"signing location syncranization\"? ", "labelEdit.sealSyncPosition.tipContent": "If you turn it off, you will not be able to re-enable this function for the entire duration of the current sending process. (You can only enable signing location synchronization again by starting a new bulk sending process)", "labelEdit.sealSyncPosition.noteText": "Note: Each signing location is configured independently and does not affect each other.", "labelEdit.sealSyncPosition.funDescTitle": "Instructions for signing location synchronization function", "labelEdit.sealSyncPosition.prepareSendNContract": "You are going to send {num} different contracts.", "labelEdit.sealSyncPosition.funDescTip.open": "When \"signing location synchronization\" is turned on, the coordinates of the signing location fields of the current contract will become effective for the other {num} contracts simultaneously.", "labelEdit.sealSyncPosition.funDescTip.close": "When \"signing location synchronization\" is turned off, it will only take effect for the current contract, and the other {num} contracts will remain as they are.", "labelEdit.sealSyncPosition.funDescTip.note": "Note: Adding or removing signing locations in the current contract will automatically take effect for the other {num} contracts. ", "labelEdit.sealSyncPosition.funDescTip.keyPositionTip": "After using keyword positioning, \"signing location synchronization\" is turned off by default for the signing locations corresponding to the keywords.", "labelEdit.sealSyncPosition.switchContractTip": "Can switch to different contracts to view the adjusted effect.", "labelEdit.sealSyncPosition.switchContactFun": "To switch contracts, click on the number of data entries in the upper right corner of the page to switch the contract you want to display.", "labelEdit.sealSyncPosition.reopenDisabledTip": "Once you turn off \"signing location synchronization\", it cannot be turned back on during this sending process.", "labelEdit.keywordPosition": "Coordinate of the field", "labelEdit.keywordMatch": "Match by keyword (support manual adjustment of position)", "labelEdit.keyword": "Keywords in the contract", "labelEdit.keywordPlaceHolder": "e.g. \"Stamp Location\"", "labelEdit.keywordNum": "Keywords at ", "labelEdit.keywordNumPlaceHolder": "Number less than 50", "labelEdit.nameError": "Please fill in the name!", "labelEdit.keywordMove": "Move offset (relative to paper size)", "labelEdit.keywordMoveX": "Horizontal movement", "labelEdit.keywordMoveY": "Vertical movement", "labelEdit.excelHeaderPosition.title": "Excel table header positioning", "labelEdit.excelHeaderPosition.keyword": "Table header keywords", "labelEdit.excelHeaderPosition.keywordPlaceHolder": "e.g. actual number received", "labelEdit.excelHeaderPosition.keywordNum": "Keywords in which positions", "labelEdit.excelHeaderPosition.referenceCol": "Reference column column name", "labelEdit.excelHeaderPosition.referenceColPlaceHolder": "such as the name of the goods", "labelEdit.excelHeaderPosition.headerKeyword": "Excel table header keywords", "labelEdit.excelHeaderPosition.result": "The effect is as follows:", "labelEdit.excelHeaderPosition.headerKeywordTipsList.0": "1. When inputting the excel form (such as goods receipts), the system will automatically set the fields in each cell under the table header keywords.", "labelEdit.excelHeaderPosition.headerKeywordTipsList.1": "2. Fields: The field names will increase in number by default. For instance, if the field name is for actual receipt, then the subsequent field names will be actual receipt_1, actual receipt_2, actual receipt_3, ...", "labelEdit.excelHeaderPosition.headerKeywordTipsList.2": "3. The first few keywords: If the document contains more than one of the same keywords, then the positioning will be to the Nth keyword (keywords in other locations do not set fields).", "labelEdit.excelHeaderPosition.setReferenceCol": " Set reference column", "labelEdit.excelHeaderPosition.setReferenceColTips.0": "1. Must be filled in, otherwise the function does not take effect.", "labelEdit.excelHeaderPosition.setReferenceColTips.1": "2. Fill in the keyword, then the field is aligned with the data of the column, and automatically terminates when it meets an empty row.", "labelEdit.excelHeaderPosition.setReferenceColTips.2": "3. The reference column supports up to 100 rows, beyond which no processing will be done.", "pointPositionDoc.pageTip": "Page {pageNum} of {pageSize}", "pointPositionDoc.nextDoc": "Go to the next document", "pointPositionDoc.checkboxName": "Alternate option {count}", "pointPositionDoc.confirmSeal": "Stamp on location of \"Content is consistent\"", "pointPositionDoc.notConfirmSeal": "Stamp on location of \"Content is inconsistent\"", "pointPositionDoc.deleteTip": "Deleted successfully", "pointPositionDoc.viewHighDpiImg": "View high resolution image|View original image", "pointPositionDoc.boxSelect.name": "Box selection", "pointPositionDoc.boxSelect.selectLabelNum": "Check {x} fields", "pointPositionDoc.boxSelect.alignType": "Field alignment", "pointPositionDoc.boxSelect.revoke": "Undo", "pointPositionDoc.boxSelect.leftAlign": "Left Alignment", "pointPositionDoc.boxSelect.bottomAlign": "Bottom Alignment", "pointPositionDoc.boxSelect.tipAccess": "Field alignment guidance", "pointPositionDoc.boxSelect.selectGuideTip": "By holding down the mouse to select a field, you can select the alignment operation in the right sidebar.", "pointPositionSite.step1": "Step 1：", "pointPositionSite.selectSigner": "Select signatory", "pointPositionSite.noneReceivers": "No signers", "pointPositionSite.step2": "Step 2：", "pointPositionSite.step3": "Step 3：", "pointPositionSite.moreConfig": "Additional Configuration", "pointPositionSite.multipleSigners": "This signatory has enabled multi-signatory feature", "pointPositionSite.dragSignaturePosition": "Drag the signing location", "pointPositionSite.signField": "Signing field", "pointPositionSite.tempField": "Temporary field", "pointPositionSite.businessField": "Business field", "pointPositionSite.addBusinessField": "Add business field", "pointPositionSite.manageBusinessField": "Manage business fields", "pointPositionSite.tempFieldFillTip": "The fields set up can be filled in by the signer", "pointPositionSite.searchBusinessField": "Please enter", "pointPositionSite.edit": "Edit", "pointPositionSite.decorateField": "Contract decoration", "pointPositionSite.optional": "(optional)", "pointPositionSite.wordBillLabelConfig": "以下字段需要配置：", "pointPositionSite.whatTempField": "What is a temporary field?", "pointPositionSite.whatTempTip.0": "Temporary fields can be used to set template variables. After setting, they will only take effect in this template and cannot be reused in other templates.", "pointPositionSite.whatTempTip.1": "The contract content filled in the temporary field does not support searching.", "pointPositionSite.know": "Got it.", "pointPositionSite.whatBusinessField": "What are business fields(contract content field)?", "pointPositionSite.whatBusinessTip.0": "Business fields can be used to set template variables, and all corporate members can reuse them when setting up a template.", "pointPositionSite.whatBusinessTip.1": "Business fields can be used to set template variables. After setting, enterprise members can reuse them when setting other templates.", "pointPositionSite.groupSignMOpenTip": "After obtaining the group console access, group management permissions, and business management permissions, clicking this button will take you to the field configuration management page.", "pointPositionSite.groupSignMJumpTip": "After obtaining the permissions, clicking this button allows you to directly add contract content fields. Once set, all company members can reuse them when configuring templates. ", "pointPositionSite.groupSignMJumpDesc": " There are two ways to obtain permissions, and meeting either condition is sufficient:", "pointPositionSite.groupSignMJumpOpt1": "1. Obtain the 'Business Management' permission within the group management authority.", "pointPositionSite.groupSignMJumpOpt2": "2. Obtain the 'Business Management' permission within the company roles.", "pointPositionSite.CommonSignMOpenTip": "After obtaining the business management authority of the company console, you can directly add contract content fields. Once set, all company members can reuse them when configuring templates.", "pointPositionSite.CommonSignMJumTip": "After obtaining the business management authority of the company console, clicking this button will take you to the field configuration management page.", "pointPositionSite.seal": "Seal", "pointPositionSite.entSignature": "Signature of the handler", "pointPositionSite.operatorSignature": "The operator's signature", "pointPositionSite.scanSignature": "Scan code to sign", "pointPositionSite.signature": "Signature", "pointPositionSite.confirmRemark": "Fail to meet the requirements in the notes regarding the seals.", "pointPositionSite.confirmSeal": "Stamp for inquiries", "pointPositionSite.signDate": "Date Signed", "pointPositionSite.text": "Text", "pointPositionSite.singleBox": "Radio", "pointPositionSite.multipleBox": "Checkbox", "pointPositionSite.comboBox": "DropDown", "pointPositionSite.watermark": "Watermark", "pointPositionSite.decorataRidingSeal": "Paging stamp", "pointPositionSite.picture": "Picture", "pointPositionSite.innerSignComment": "Annotation", "pointPositionSite.number": "Number", "pointPositionSite.bizDate": "Date", "pointPositionSite.singlePageRidingSealTip": "You can't add paging stamp to a single-page document", "pointPositionSite.ridingSealTip": "Set pageing stamp on all documents by default. But it won't take effect on the document which the signatory doesn't need to stamp on.", "pointPositionSite.ridingSealSendTip": "If the contracting party does not participate in the document sealing, the corresponding document cross stitch seal will not take effect.", "pointPositionSite.addContentFieldSteps.0": "At the Company console - Business field management - Contract content field page, configure a field named “xxx”.", "pointPositionSite.addContentFieldSteps.1": "After about 15 minutes, the \"xxx\" field will appear in the Contract management - List config - Hidden field for you to use.", "pointPositionSite.ridingSealDocConfig": "Whether to set paging-stamp for the following documents", "pointPositionMiniDoc.superTotalContract": "Total {num} count,only 500 count are displayed, please select", "pointPositionMiniDoc.totalContract": "Total {num} count, please select", "pointPositionMiniDoc.totalContractAfter": "Display of articles", "pointPositionMiniDoc.contractSwitchTip": "A record corresponds to a row of data imported by batch to the Excel table", "pointPositionMiniDoc.document": "Documents", "pointPositionMiniDoc.documentsLength": "{documentsLength} documents in total", "pointPositionMiniDoc.pager": "Page number", "pointPositionMiniDoc.page": "Page", "pointPositionMiniDoc.totalPages": "page number", "pointPositionMiniDoc.goToPage": "Skip to:", "pointPositionMiniDoc.skipRiskTip": "The current contract to be sent contains more than 500 enterprise signatories, no annual audit risk warning you can check the annual audit of the opposite party in the file cabinet, confirm the error before sending", "pointPositionMiniDoc.findNextSignPosition": "Locate Signature Fields", "pointPosition.saveTemplateTip": "Important Notice", "pointPosition.save": "Continue to save", "pointPosition.send": "Continue to send", "pointPosition.hasSameLabelTip": "A field with the same name of a different type already exists.", "pointPosition.needChangeNameTip": "A field with the same name already exists. Please modify the name.", "pointPosition.synLabel": "The value of {num} fields with the same name have been updated to the current field value.", "pointPosition.saveSuc": "Saved successfully", "pointPosition.isToPermissions": "Restrict the template user's permission to modify the contract before sending the contract, which can regulate the user's operation behavior", "pointPosition.remind": "Tip", "pointPosition.goSet": "Go to Settings", "pointPosition.notGoSet": "Not Now", "pointPosition.nowSignText": "The document needs to be signed, whether to sign immediately?", "pointPosition.nowSignTip": "Tip", "pointPosition.nowSign": "sign now", "pointPosition.laterSign": "sign later", "pointPosition.contractDispatchApply": "Set contract approval", "pointPosition.riskTips": "Annual audit risk warning", "pointPosition.riskTipsCancel": "Cancel sending", "pointPosition.riskTipsConfirm": "All send", "pointPosition.realNameAnnualVerify": "Real name annual review", "pointPosition.realNameAnnualVerifyRecords": "Annual review records", "pointPosition.customInfoAnnualVerify": "Custom information annual audit", "pointPosition.viewAnnualVerifyINfo": "View profile details", "pointPosition.entName": "Business name", "pointPosition.operation": "Operation", "pointPosition.annualVerifyTime": "Annual review time", "pointPosition.annualVerifyStatus": "Annual review status", "pointPosition.annualVerifyCondition": "Annual review conditions", "pointPosition.noDataTips": "None", "pointPosition.noRealNameAnnualVerify": "Not reviewed", "pointPosition.realNameAnnualVerifying": "Under review", "pointPosition.realNameAnnualVerified": "Reviewed", "pointPosition.noCustomInfoAnnualVerify": "Not reviewed", "pointPosition.customInfoAnnualVerifyingNo": "Annual review in progress (not submitted)", "pointPosition.customInfoAnnualVerifyingYes": "Annual review in progress (submitted)", "pointPosition.customInfoAnnualVerified": "Annual review completed", "pointPosition.deleteRidingSeal": "Delete paging-stamp", "pointPosition.deleteEntRidingSealDes": "Delete the paging-stamp place of \"{showName}\"：", "pointPosition.deleteCurrentRidingSeal": "Delete the paging-stamp place in the current document only", "pointPosition.deleteAllRidingSeal": "Delete the paging-stamp place in all documents", "labelLackTip.document": "Document", "labelLackTip.signer": "Signatory", "labelLackTip.lackLabelTips": "Signing locations not set in the following contract documents. Review files to determine if signing locations need to be added.", "labelLackTip.allLackLabelTips": "No signing position specified in any contract document. The contract cannot be sent. Please adjust the signing position or remove the signer.", "labelLackTip.goEdit": "Go to modify", "labelLackTip.operationTip": "Need to know how to only view and not participate in signing contracts? Please click here.", "labelLackTip.caseTip.1": "Option 1: Add signing instructions with contract documents to xxx signing role.", "labelLackTip.caseTip.2": "Option 2: Add xxx signing role as a CC recipient to view all contract documents. Recommend disabling notifications for this CC recipient.", "labelLackTip.caseTip.3": "Option 3: xxx signing role can view contract documents as sender, approver, or with company contract viewing permission.", "labelLackTip.ridingSealInvalid": "The following contract document will not show paging-stamp:", "labelLackTip.onePageLimint": "The contract is only one page in total, and there is no need to add the paging-stamp", "labelLackTip.noSealLimit": "{name} is not a signatory of this contract", "signCharge.deductPublicNotice": "When \"To individual contract\" copies is insufficient, it will be deducted from \"To company contract\" copies", "signCharge.isReceivePayer": "The contract will be paid by the signer, the signatory designated by you", "signCharge.isCCReceiverPayer": "This contract will be paid by your designated cc", "signCharge.CCReceiverPayerFailTip": "The cc has insufficient balance or does not support your designated CC to pay", "signCharge.charge": "Billing: ", "signCharge.units": "{num} copies", "signCharge.contractToPrivate": "to individual contract", "signCharge.contractToPublic": "to company contracts", "signCharge.costTips.1": "To company contract: Contracts with company signatories (excluding the sender)", "signCharge.costTips.2": "To individual contract: Contracts with no company signatory (excluding the sender)", "signCharge.costTips.3": "The number of billing shares is calculated based on the number of copies of the file.", "signCharge.costTips.4": "Number of billed copies = Number of document copies × Number of user groups (rows) imported in bulk", "signCharge.accountCharge.notice": "The contract is billed on the basis of the number of participating accounts", "signCharge.accountCharge.able": "can be sent normally", "signCharge.accountCharge.unable": "Not enough accounts available for use. Please contract BestSign customer service.", "signCharge.confirm": "Confirm", "signCharge.cancel": "Cancel", "signCharge.toCharge": "To Top-up", "signCharge.contractNeedCharge.1": "The number of available contract copies is insufficient and the contract cannot be sent.", "signCharge.contractNeedCharge.2": "The number of available contract copies is insufficient. Please contact the administrator for top-up.", "signCharge.contractNeedCharge.3": "The balance of the dedicated package is insufficient. Please click the recharge button below to complete the top-up.", "signCharge.signRole": "After the contract is sent successfully, automatic signing will be done for the following signers:", "signCharge.signRoleNone": "none", "signCharge.signProxy": "The current template has been used for \"Business Collaboration\" and cannot be used for the Send and Sign function.", "signCharge.cannotSign": "You cannot use the \"Send and Sign\" function.", "signCharge.signRoleTerm": "Automatic signing is allowed for signers that meet the following criteria:", "signCharge.signRoleTermBtn": "Collapse", "signCharge.signRoleTermOpenBtn": "Expand", "signCharge.sendAndSignTerms.0": " (1) The \"template special seal\" has been assigned to the signer.", "signCharge.sendAndSignTerms.1": " (2) The signer's operator account is the current account, and has been given a \"special seal\".", "signCharge.sendAndSignTerms.2": " (3) The signer is the first sequential signer of the contract, or \"sequential signing\" is not enabled for the contract.", "signCharge.sendAndSignTerms.3": " (4) The signer has not enabled review prior to signing, or review before sending is not enabled for the contract.", "signCharge.sendOnly": "Sending the contract", "signCharge.sendAndSign": "Send and sign the contract", "signCharge.verCodeInputErr": "Get the verification code first!", "signCharge.lackVerCode": "Please Enter the Verification Code First", "signCharge.timingSend": "Sending contracts at a specified time", "signCharge.sender": "Sender Company", "signCharge.switchPayerTip": "Note: To avoid contract process blockage, please reach an agreement with the paying signatory in advance.", "signCharge.noSignInAllTip": "Contracting party: {<PERSON><PERSON><PERSON>} has not participated in the signing of all documents and cannot be set as the paying party of the contract. Do you want to cancel the setting of the paying party", "signCharge.payer": "Payer", "signCharge.payerChargeUsageDesc": "Recipient Pays Feature Introduction", "signCharge.switchPayer": "Recipient Pays:", "signCharge.feePayer": "Fee Payment:", "signCharge.keyAdv": "Key Advantages:", "signCharge.decreaseCost": "Cost Reduction: ", "signCharge.optMulti": "Flexible Operation: ", "signCharge.connectToOpen": "Contact BestSign to enable the paid party function", "signCharge.connectAdminToOpen": "You do not have operation permission. Please contact administrator {adminAccount}", "signCharge.decreaseCostDesc": "The signing party pays the fee, effectively reducing your cost burden.", "signCharge.optMultiDesc": "Freely choose the payer according to business practices or mutual agreement.", "signCharge.payerChargeUsageTip1": "The selected signing party role pays the contract fee. Your company and other contract participants will not be charged.", "signCharge.payerChargeUsageTip2": "Based on the payer's contract balance, fees are automatically deducted or a recharge prompt is given when signing the contract.", "sendPrepare.selectTemplate": "Select the template", "sendPrepare.selectTemplateFileTip": "Please select the template document first", "sendPrepare.batchImportExcel": "Bulk Send by Excel", "sendPrepare.batchReplaceExcel": "Bulk sending different contracts", "sendPrepare.sendContract": "Single Send", "sendPrepare.allTemplate": "All templates", "sendPrepare.selectModeTitle": "Template view", "sendPrepare.selectDocument": "Please select Document", "sendPrepare.viewDetail": "View Details first", "sendPrepare.sendGuideStep1": "How to select a template？", "sendPrepare.sendGuideStep2": "How to send contracts in bulk?", "sendPrepare.sendGuideStep3": "What should I do if I don't want to send contracts using templates?", "sendPrepare.sendGuideDialogText1": "Find the templates corresponding to the scenario, select one or more documents from them, and send these documents to the signing party all at once. | Multiple documents that are often sent together can be configured as \"Template Document Combination\" for quick and easy selection.", "sendPrepare.sendGuideDialogText2": "Send/initiate contracts individually: one document at a time, or a combination of documents at a time. | Send contracts in a batch using Excel: to send N copies of contracts to N people at a time. These contracts are template contracts. |  Send contracts in a batch: to send N copies of contracts to N people at a time. These contracts are uploaded locally as zip packages, each with different content.", "sendPrepare.sendGuideDialogText3": "Select the documents with + (or check the documents in \"documents to be uploaded\"), click \"Upload document\" button to add local files. (If there is no such document, please go to the template management page to create one.)", "sendPrepare.allMode": "Show all", "sendPrepare.federationMode": "Show only template document combinations", "sendPrepare.documentMode": "Show only template documents", "sendPrepare.emptyContract": "Blank contract", "sendPrepare.noTemplate": "You don't have any template", "sendPrepare.allocateTemplate": "Please contact the company administrator to assign templates for you", "sendPrepare.allocateTemplatePermission": "Or: You can contact the administrator to assign you the “Create Template” permission in the Role Management module of the Company Console. Then you can directly send the contract or create a template to send the contract.", "sendPrepare.sendQuickly": "Quick contract initiation without the need to create a template", "choseBoxForReceiver.dataNeedForReceiver": "Information to be submitted by the signatory", "choseBoxForReceiver.dataFromDataBox": "Signatory information needs to be collected through a certain file cabinet.", "choseBoxForReceiver.searchTp": "Please enter the name or number of the file cabinet", "choseBoxForReceiver.search": "Search", "choseBoxForReceiver.boxNotFound": "No file cabinet found", "choseBoxForReceiver.cancel": "Cancel", "choseBoxForReceiver.confirm": "OK", "dataBoxInvite.title": "Invite your partners", "dataBoxInvite.step1": "Share the link with your partners to create an company account in advance.", "dataBoxInvite.step2": "Partners authorized by link/QR code will appear in the address book automatically.", "dataBoxInvite.step3": "More partner management activities in File +", "dataBoxInvite.imgName": "Share the QR code for collection", "dataBoxInvite.saveQrcode": "Save the QR code locally", "dataBoxInvite.copy": "Copy", "dataBoxInvite.copySuccess": "Copy successfully", "dataBoxInvite.copyFailed": "Co<PERSON> failed", "selectSender.addReceiver": "Please add a signatory", "selectSender.currentSender": "Current sender:", "selectSender.toBeDetermined": "[Select the company]", "selectSender.tips.0": "This template has been authorized with entrusted sending, and each signing notification is displayed in the name of the authorizing company.", "selectSender.tips.1": "If there are multiple authorizing companies in the same contract signing, entrusted sending is not allowed", "selectSender.multiSenderSelectTip": "If you have multiple businesses or companies, you can switch different identities to send contracts here.", "selectSender.sendEnt": "Sender's company", "selectSender.sendEntTip": "The scope of the optional sending companies is: (1) companies whose accounts are authorized by the template to \"send contracts\"; (2) companies whose accounts have been managed by the \"Group Console - Group Management Permissions\".", "selectSender.sendContractNoPermissionTip": "To use this template to send contracts on behalf of {entName}, you need to obtain the \"Entrusted sending\" permission from XXX for the template.We suggest that you contact the template creator or a company member with template assignment permissions", "selectSender.viewTempaltePermission": "View my permissions in this template", "selectSender.noSendPermission": "No permission to send for this enterprise yet", "downloadPwdDialog.copy": "Copy", "downloadPwdDialog.copySucc": "Copy successfully", "downloadPwdDialog.copyFailed": "Co<PERSON> failed", "downloadPwdDialog.downloadTip": "Downloading Tips", "downloadPwdDialog.downloadContentTip": "The contract file 《{fileName}.zip》ou have downloaded has been encrypted by the system and the password for decompression is as follows:", "downloadPwdDialog.downloadCodeTip": "Note: The password is sent randomly with each download!", "contractItem.emptyContract": "Document to be uploaded", "contractItem.uploadDoc": "Upload your contract", "contractItem.replaceDoc": "Replace your document", "contractItem.addAttachment": "Add attachments", "contractItem.uploading": "Uploading ...", "contractItem.pageCount": "{num} Page", "contractItem.more": "More...", "contractItem.configAttachment": "Configure attachment fields", "contractItem.federationTip": "（If you are using a template to send contracts with multiple documents, assume that there are documents a,b,and c, with Scenario 1 often needs to send a and b, and scenario 2 often needs to send a,b,and c. Then you can combine the documents in advance and select the corresponding combination directly when sending.）", "contractItem.federations": "Template document combination", "contractItem.fillDocument": "Upload contract document", "contractItem.uploadDocument": "Upload document ", "contractItem.uploadDocumentContinue": "Continue uploading documents ", "contractItem.temporarySaveTip": "The current contract has not been sent out yet. | When you select this template to send contract again, you can continue to send this \"saved\" contract within 7 days | Click on the template to find the unsent draft contract.", "contractItem.notRemind": "Do not remind me next time", "contractItem.interanlSignTip": "The current template is set to \"internal document signing scenarios only\" and should not be used for formal contracts (such labor contracts).", "contractItem.attachedToBoxTip": "This template is already bound to the entrusted sending and signing function of the file cabinet, and temporary documents and attachment fields cannot be created in it.", "contractItem.replaceTip1": "The new document you are about to use for replacement contains only {page} pages, while page {pageStr} of the original document before replacement may contain the following field information, such as seal, signature, signing date, and contract content fields.\nYou need to delete these fields before replacing them again. The steps are as follows:", "contractItem.replaceTip2": "1. Continuously click the \"Next\" button in the upper right corner of the page to enter the \"Specify Signing Location\" page.", "contractItem.replaceTip3": "2. Carefully check page {pageStr} and manually delete the above field information.", "contractItem.replaceTip4": "3. Return to the current \"Upload Document\" page and perform the replacement operation again.", "contractItem.replaceTip5": "4. After successful replacement, enter the \"Specify Signing Location\" page again and manually add the deleted fields to the new document to avoid affecting your business.", "descriptionFields.newField": "Add fields", "descriptionFields.title": "Field configuration", "descriptionFields.syncDoc": "Sync to other documents in the template", "descriptionFields.placeholder": "Please enter fields", "descriptionFields.cancel": "Cancel", "descriptionFields.confirm": "Confirm", "descriptionFields.saveSuc": "Saved successfully", "getSeal.selectSeal": "Select seals", "getSeal.chooseApplyPerson": "Select applicants", "getSeal.getSealBtn": "You are requesting the following seals", "getSeal.nowApplySealList": "You are requesting the following seals", "getSeal.chooseApplyPersonToDeal": "Please select the operator to seal, and your application and contract will be forwarded to the selected person for processing(You can still continue to review and follow up on this contract).", "getSeal.chooseApplyPersonToMandate": "Please select the seal owner, and once the selected person is notified and approved, you will be granted access to the seal and will be able to use the seal to stamp and sign the contract.", "getSeal.cancel": "Cancel", "getSeal.confirm": "Confirm", "getSeal.sealApplySentPleaseWait": "The application for seal assignment has been sent. Please wait for the approval. Or you can choose another way of stamping.", "getSeal.entNoSeal": "Your company has not uploaded a seal yet.", "getSeal.contactGroupAdminToDistributeSeal": "Please contact the group administrator to assign a seal.", "getSeal.getSealTip": "You need to obtain a corporate seal before you can view this contract.", "authIntercept.title": "You are required to:", "authIntercept.name": "The name is:", "authIntercept.id": "The ID number is:", "authIntercept.descNoAuth1": "Please confirm that the above identity information is your own and use it for real name authentication.", "authIntercept.descNoAuth2": "Once your real name is verified, you can view and sign the contract.", "authIntercept.descNoSame1": "Signing a contract with       ID.", "authIntercept.descNoSame2": "This does not match the existing real name information of your currently logged-in account.", "authIntercept.tips": "Note: The contract can be signed only if the identity information is consistent.", "authIntercept.goOn": "It's me. Start authentication", "authIntercept.goMore": "Go to additional authentication", "authIntercept.authTip": "Start real name authentication.", "authIntercept.viewAndSign": "After completing the authentication, you can view and sign the contract.", "authIntercept.tips2": "Note: The business name shall be completely consistent to view and sign the contract.", "authIntercept.requestOtherAnth": "Request verification by others", "authIntercept.goAuth": "Go to Real Name Authentication", "authIntercept.requestSomeoneList": "Request the following people to complete real name authentication", "authIntercept.ent": "Company", "authIntercept.entName": "Company name", "authIntercept.account": "Account", "authIntercept.accountPH": "Cell phone number or email address", "authIntercept.send": "Send", "authIntercept.lackEntName": "Please enter the company name", "authIntercept.errAccount": "Please fill in the correct email or cell phone number", "authIntercept.successfulSent": "<PERSON><PERSON> successfully", "authIntercept.me": "me", "authIntercept.myself": "myself", "authIntercept.reAuthBtnTip": "I am the actual user of the current cell phone number.", "authIntercept.reAuthBtnContent": "The existing real name of this account will be rejected after the real name is changed. Please confirm.", "authIntercept.cancel": "Cancel", "authIntercept.confirmOk": "Confirm", "authIntercept.goHome": "Back to contract list page>>", "authIntercept.authInfo": "The real name identity of your current account is detected as", "authIntercept.finishAuth": "Complete the real name for signing contracts in compliance", "authIntercept.ask": "Do you want to continue to sign with your current account?", "authIntercept.reAuthBtnText": "Yes, I want to use the current account to sign again with my real name", "authIntercept.changePhoneText": "No, contact the sender to change the cell phone number for signing", "authIntercept.changePhoneTip1": "At the request of the sender, please contact", "authIntercept.changePhoneTip2": ", change the signature information (cell phone number/ name) and specify that you will sign the contract.", "applyJoinEnt.beenAuthenticated": "Real name has been used", "applyJoinEnt.assignedIdentity": "The signatory provided by the sender is:", "applyJoinEnt.entBeenAuthenticated": "The company has used real names, and the main administrator's information is as follows", "applyJoinEnt.entAdminName": "Administrator's name:", "applyJoinEnt.entAdminAccount": "Administrator's account number:", "applyJoinEnt.applyToBeAdmin": "I want to request to become the main administrator", "applyJoinEnt.contactToJoin": "Contact the administrator to join the company", "applyJoinEnt.applicant": "Applicant", "applyJoinEnt.inputYourName": "Please enter your name", "applyJoinEnt.account": "Account", "applyJoinEnt.send": "Send", "applyJoinEnt.contract": "Contract", "applyJoinEnt.sendWishToJoin": "\"You can request to become an administrator through your account, or you can send a request to the administrator to join the company.\"", "applyJoinEnt.applyToJoin": "You are not yet a member of the company and cannot view or sign the {alias}. Do you want to apply to join?", "applyJoinEnt.sentSuccessful": "<PERSON><PERSON> successfully", "receiverItemDisclaimer.title": "Tips for using the function", "receiverItemDisclaimer.desc.0": "According to the relevant provisions of the Electronic Signature Law, a legally valid electronic contract must be accessed and used by the signatory at any time. Prohibiting the signatory from downloading or viewing it would violate the requirements of the Electronic Signature Law.", "receiverItemDisclaimer.desc.1": "Please ensure that the signatory can view and download the signed agreement.", "receiverItemDisclaimer.desc.2": "I have read and agreed to the above content.", "receiverItemDisclaimer.confirm": "Continue", "batchOrAllOperateContract.iKnow": "I know", "batchOrAllOperateContract.oneTypeSeal": "The same seal to be used by the same company", "batchOrAllOperateContract.moreTypeSeal": "Use different seals for different contracts", "batchOrAllOperateContract.noSealAuthTip": "As you have not yet obtained the necessary permissions to sign this batch of contracts, you are unable to perform the bulk signing operation. Suggest that you sign these contracts separately.", "batchOrAllOperateContract.noSealAuthTipTitle": "Common causes：", "batchOrAllOperateContract.noSealAuthTip1": "1、You have not yet joined the contracting company and cannot sign its contract.", "batchOrAllOperateContract.noSealAuthTip2": "2、You do not have a seal yet and cannot sign the contract.", "batchOrAllOperateContract.noSealAuthTip3": "3、Your company requires additional authorization to sign contracts sent by certain sender companies, but you lack this authorization.", "batchOrAllOperateContract.amountOfContract": "copy of the contract", "batchOrAllOperateContract.noAuthEnt": "Enterprises without real names", "batchOrAllOperateContract.personSign": "Personal Default Signature", "batchOrAllOperateContract.noChangeSeal": "There is no need to change the seal data. The default seal has been used for you.", "batchOrAllOperateContract.noSealTip": "There is no switch seal", "batchOrAllOperateContract.selectSeal": "Select seal", "batchOrAllOperateContract.useSeal": "Use", "batchOrAllOperateContract.changeSeal": "Change seal", "batchOrAllOperateContract.batchSignOtherTabTip": "(may be used in non-selected contracts)", "batchOrAllOperateContract.batchSignOtherTab": "More seals of contracting parties", "batchOrAllOperateContract.batchSignCurTab": "The seal of the contracting party of the current contract page", "batchOrAllOperateContract.moreContractSealTip": "The following conditions must be met before you can use it: 1. You have selected \"Only checked contracts\" in the previous step \"Scope of contracts signed in bulk\"; 2. The company has assigned you more than one seal", "batchOrAllOperateContract.signFooterTip": "The more the contracts, the longer it will take for the batch signing to finish. Your patience will be appreciated.", "batchOrAllOperateContract.yes": "yes", "batchOrAllOperateContract.no": "no", "batchOrAllOperateContract.reject": "Reject", "batchOrAllOperateContract.agree": "Agree", "batchOrAllOperateContract.cancel": "Cancel", "batchOrAllOperateContract.confirm": "Confirm", "batchOrAllOperateContract.continue": "Continue", "batchOrAllOperateContract.continueSign": "Continue", "batchOrAllOperateContract.batch": "Bulk", "batchOrAllOperateContract.nowBatch": "The bulk", "batchOrAllOperateContract.contractRange": " includes:", "batchOrAllOperateContract.operate": "Operation", "batchOrAllOperateContract.allSelect": "A total of ", "batchOrAllOperateContract.changeStatusTip": "Contracts other than \"overdue\" contracts will not be operated. The status of \"Overdue\" contracts will be changed to \"Signing\", and the deadline for contract signing is 7 days later. Do you need a reminder to be pushed to accounts that have not signed the contract?", "batchOrAllOperateContract.changeStatusSupplement": "The \"time frame for signing\" set for signatories will no longer be in effect", "batchOrAllOperateContract.approvalTip": " contracts are selected，contracts that do not require your approval will not be operated。Please select the approval result", "batchOrAllOperateContract.outputTip": " contracts are selected.Does the export result include all subcontracts of the multi-document contract?", "batchOrAllOperateContract.revokeTip": " contracts are selected, among which those do not require your signature will not be operated.", "batchOrAllOperateContract.remindTip": " contracts are selected, among which those do not require your signature will not be operated.", "batchOrAllOperateContract.transferTip": " contracts are selected", "batchOrAllOperateContract.batchSignTip": " contracts are selected, among which those do not require your signature will not be operated.The seal used for this batch signing is:", "batchOrAllOperateContract.batchTagTip": "If this contract is not sent by your company, then you can not put label on it.", "batchOrAllOperateContract.outputFailTip": "Select at least one contract in the contract list to export", "batchOrAllOperateContract.downloadTip": "copies of contract", "batchOrAllOperateContract.batchOperateResultPage": "Bulk operation result", "batchOrAllOperateContract.toHomePage": "Go to Home", "batchOrAllOperateContract.refresh": "Refresh page", "batchOrAllOperateContract.logTips": "The more quantity, the longer the batch operation time required. You can first process other tasks before entering this page to view. (Entrance: 1. Contract Management ->Batch Operation Record; 2. Archive+->Batch Task Center)", "batchOrAllOperateContract.operateName": "Operation name", "batchOrAllOperateContract.operateTime": "Operation time", "batchOrAllOperateContract.operateResult": "Operation result", "batchOrAllOperateContract.operateLog": " operation log", "batchOrAllOperateContract.remark": "Remark", "batchOrAllOperateContract.contractTask": "Contract management bulk task ", "batchOrAllOperateContract.archiveTask": "File+ bulk task", "batchOrAllOperateContract.useRecordTask": "Usage record download task", "batchOrAllOperateContract.createTime": "Operation time", "batchOrAllOperateContract.bizName": "Name of the file cabinet", "batchOrAllOperateContract.bizId": "File cabinet ID", "batchOrAllOperateContract.total": "Data volume", "batchOrAllOperateContract.cost": "Estimated time consumption", "batchOrAllOperateContract.fileStatus": "File status", "batchOrAllOperateContract.archiveExport": "Export of the file cabinet listed information", "batchOrAllOperateContract.archiveImport": "File cabinet pre import", "batchOrAllOperateContract.collectImport": "Collection Import", "batchOrAllOperateContract.audit": "Full amount of contract pre-approval", "batchOrAllOperateContract.realNameAnnualVerify": "Real name authentication annual audit", "batchOrAllOperateContract.customInfoAnnualVerify": "Custom information annual audit", "batchOrAllOperateContract.transfer": "Transfer of Information", "batchOrAllOperateContract.queryResultsExport": "相对方企业查询结果导出", "batchOrAllOperateContract.status0": "Waiting to proceed", "batchOrAllOperateContract.status1": "In progress, please wait...", "batchOrAllOperateContract.status2": "Expires in {day} days", "batchOrAllOperateContract.status3": "Expired", "batchOrAllOperateContract.status4": "Task failed", "batchOrAllOperateContract.status5": "Canceled", "batchOrAllOperateContract.download": "Download the file", "batchOrAllOperateContract.useRecordExport": "Export usage records", "batchOrAllOperateContract.batchChangeStatus": "Bulk extension of signing date", "batchOrAllOperateContract.batchSetTag": "Bulk set tag", "batchOrAllOperateContract.batchArchive": "Bulk archive", "batchOrAllOperateContract.batchRemind": "Bulk remind", "batchOrAllOperateContract.batchRevoke": "Bulk revoke", "batchOrAllOperateContract.batchImport": "Contract import", "batchOrAllOperateContract.batchSend": "Bulk Send", "batchOrAllOperateContract.batchExport": "Bulk Export", "batchOrAllOperateContract.batchExportDownload": "Download", "batchOrAllOperateContract.view": "View", "batchOrAllOperateContract.batchExportFail": "Bulk export fail", "batchOrAllOperateContract.batchTransfer": "Bulk transfer", "batchOrAllOperateContract.batchApproval": "Bulk approval", "batchOrAllOperateContract.batchSign": "Bulk sign", "batchOrAllOperateContract.downloadLinkFailure": "The link is no longer available", "batchOrAllOperateContract.downloadCancel": "Cancel", "batchOrAllOperateContract.downloadError": "Download error", "batchOrAllOperateContract.batchDownload": "Bulk download", "batchOrAllOperateContract.zip": "Compressed package", "batchOrAllOperateContract.batchModifyLife": "Bulk modification of contract expiration date", "batchOrAllOperateContract.batchModifyTip": "Contract expiration time ≠ contract deadline. If you set contract reminder, please set contract deadline separately.", "batchOrAllOperateContract.notStart": "Doing", "batchOrAllOperateContract.doing": "Doing", "batchOrAllOperateContract.discontinue": "Terminated", "batchOrAllOperateContract.viewProgress": "Viewing progress", "batchOrAllOperateContract.operateProgress": "Operation progress", "batchOrAllOperateContract.tip": "Tip", "batchOrAllOperateContract.discontinueOperate": "Terminate the task", "batchOrAllOperateContract.confirmDiscontinue": "After the task is terminated, the unprocessed contract needs to be operated again. ", "batchOrAllOperateContract.operateSuccess": "Success", "batchOrAllOperateContract.taskTerminated": "The Task has been terminated", "batchOrAllOperateContract.done": "Done", "batchOrAllOperateContract.detail": "A total of {totalCount} copies need to be operated, and now {nowCount} copies have been operated.The operation result is based on the final operation log.", "batchOrAllOperateContract.moreBatch": "More bulk operation", "batchOrAllOperateContract.batchLog": "Bulk log", "batchOrAllOperateContract.feedback": "questionnaire feedback", "batchOrAllOperateContract.tagTip": "Tags created by a company can only be set to contracts sent by that company, not to contracts sent by other companies.", "batchOrAllOperateContract.changeTip": "If you continue the operation, you can change the status of \"overdue\" contracts to \"signing\" in order to restart contract signing.", "batchOrAllOperateContract.remindFooterTip": "No reminder for users who have been reminded 6 times during the day or whose turn for signing/review has not yet come.", "batchOrAllOperateContract.signTip.tip1": "Contracts that contain the following signing requirements cannot be signed in batches on the computer web page:", "batchOrAllOperateContract.signTip.tip2": "（1）Contracts for which the following actions must be completed before signing：Review before signing, read the full text of the contract, fill in the business fields of the contract, submit additional information about the contract, submit information about the signatories, complete real name authentication, and obtain a designated seal/business checking seal.", "batchOrAllOperateContract.signTip.tip3": "（2）The following operations need to be completed when signing a contract：manual signing is required.", "batchOrAllOperateContract.signTip.tip4": "We recommend that you sign such contracts one by one individually, or scan the QR code to sign in batches on your cell phone.", "batchOrAllOperateContract.downloadFooterTip": "If an enterprise does not grant you the download permission, you cannot download the contract of the enterprise.", "batchOrAllOperateContract.setOption1": "Set only the contracts that are checked", "batchOrAllOperateContract.changeOption1": "Change only the contracts that are checked", "batchOrAllOperateContract.revokeOption1": "Revoke only the contracts that are checked", "batchOrAllOperateContract.transferOption1": "Transfer only the contracts that are checked", "batchOrAllOperateContract.outputOption1": "Export only the contracts that are checked.", "batchOrAllOperateContract.approvalOption1": "Approval only the contracts that are checked.", "batchOrAllOperateContract.signOption1": "Sign only the contracts that are checked.", "batchOrAllOperateContract.downloadOption1": "Download only the contracts that are checked", "batchOrAllOperateContract.setOption2": "Set all contracts in the current list, including unchecked contracts on the current page of the list and contracts on other pages of the list. You can adjust the range of contracts for batch operation by using the archive folder or search function.", "batchOrAllOperateContract.changeOption2": "Change all contracts in the current list, including unchecked contracts on the current page of the list and contracts on other pages of the list. You can adjust the range of contracts for batch operation by using the archive folder or search function.", "batchOrAllOperateContract.revokeOption2": "Revoke all contracts in the current list that are approved and signed, including contracts that are unchecked on the current page of the list and contracts on other pages of the list. By using the archive folder or the search function, you can adjust the range of contracts for the batch operation.", "batchOrAllOperateContract.remindOption2": "Remind all contracts in the current list that are approved and signed, including contracts that are unchecked on the current page of the list and contracts on other pages of the list. By using the archive folder or the search function, you can adjust the range of contracts for the batch operation.", "batchOrAllOperateContract.transferOption2": "Transfer all contracts in the current list, including unchecked contracts on the current page of the list and contracts on other pages of the list. You can adjust the range of contracts for batch operation by using the archive folder or search function.", "batchOrAllOperateContract.outputOption2": "Export all the contracts in the current list, including contracts those are unchecked on the current page of the list and contracts on other pages of the list. By using the archive folder or the search function, you can adjust the range of contracts for the bulk operation.", "batchOrAllOperateContract.approvalOption2": "Approval all pending contracts in the current list, including contracts that are unchecked on the current page of the list and contracts on other pages of the list. By using the archive folder or the search function, you can adjust the range of contracts for the batch operation.", "batchOrAllOperateContract.signOption2": "Sign all pending contracts in the current list, including contracts that are unchecked on the current page of the list and contracts on other pages of the list. By using the archive folder or the search function, you can adjust the range of contracts for the batch operation.", "batchOrAllOperateContract.downloadOption2": "Download all contracts in the current list, including unchecked contracts on the current page of the list and contracts on other pages of the list. You can adjust the range of contracts for batch operation by using the archive folder or search function.", "batchOrAllOperateContract.batchExportTip": "No more than 10,000 contract details. Higher version can export more contract details at one time.", "batchOrAllOperateContract.contractOutPut.outOfQuantity": "You have selected {num} contracts which exceeding the limit of 50000.  They cannot be exported in bulk.", "rejectSigner.tipTitle": "Rejection and re-sign alert", "rejectSigner.tipContent": "Please check the signature fields (signature or seal or fields filled in by the signatory) that need to be rejected and then click the \"Reject and Re-Sign\" button.", "rejectSigner.noTip": "No more alerts", "rejectSigner.iKnow": "Got it.", "rejectSigner.rejectBtn": "Rejection and Re-sign", "rejectSigner.noOptionTip": "Please check the signature fields (signature or seal or fields filled in by the signatory) that need to be rejected and then click the \"Reject and Re-Sign\" button.", "rejectSigner.writeMustKnow": "Instructions for re-signing", "rejectSigner.confirm": "Confirm", "rejectSigner.cancel": "Cancel", "rejectSigner.success": "Rejection successful", "rejectSigner.fail": "Users who are re-signing cannot reject the re-signing. Please wait for the user to finish signing first.", "rejectSigner.mustKnowPlaceHolder": "Optional, limited to 255 characters", "rejectSigner.mustKnowTip": "Please improve the signing instructions to help the signer re-sign smoothly.", "rejectSigner.placeTop": "Set overlapping top level placeholders to the bottom", "templateListDynamicEntryConfirm.title": "Tips", "templateListDynamicEntryConfirm.content.0": "It is detected that you have upgraded to the new contract. Are you going to continue to upgrade the new dynamic template? After upgrading the dynamic template, the new contract functions can be used:", "templateListDynamicEntryConfirm.content.1": "Including template attachment addition, template document combination, send reviewed contracts, and other functions. Please contact customer service or customer success manager for upgrading.", "templateListDynamicEntryConfirm.content.2": "Note: After upgrading, the historical dynamic template will be transferred to the new contract template. If you used html dynamic template before, the template will not be synchronized after upgrade. Please save the template data in advance.", "templateListDynamicEntryConfirm.cancel": "No more tips", "templateListDynamicEntryConfirm.confirm": "Confirm", "templateListOpenDynamicConfirm.title": "Is dynamic template enabled?", "templateListOpenDynamicConfirm.content.0": "With dynamic templates turned on, you can:", "templateListOpenDynamicConfirm.content.1": "Set business fields directly within the uploaded WORD document, without dragging and dropping them on the web page.", "templateListOpenDynamicConfirm.content.2": "Edit template manuscripts online. ", "templateListOpenDynamicConfirm.content.3": "Insert dynamic forms with any number of rows and columns.", "templateListOpenDynamicConfirm.tip": "Contact our sales employee to open it right away.", "templateListOpenDynamicConfirm.confirm": "Got it.", "templateDynamicPosition.notice": "Reminder", "templateDynamicPosition.initLoadingTip": "Template fields and editor initialization in progress. Please wait ...", "templateDynamicPosition.saveLoadingTip": "Please do not refresh or close the page while data is being saved to avoid data loss.", "templateDynamicPosition.modifyNotice": "Modification of template field names must be done through the pop-up window on the right side of the page. Direct modification in the text is not effective and will be overwritten by the name in the pop-up window.", "templateDynamicPosition.modifyName": "After system testing, some field name modification operations do not conform to the rules. Please check and save again.", "templateDynamicPosition.modifyClose": "Got it.", "templateDynamicPosition.initDocError": "Document initialization failed", "templateDynamicPosition.helpPoper": "The dynamic template has been fully upgraded. For more tips on using it, please click User Help to view it.", "templateDynamicPosition.help": "User Help", "templateDynamicPosition.preview": "Effect preview", "templateDynamicPosition.someTextNoName": "Please fill in the name for each text label.", "templateDynamicPosition.allSignDone": "Please specify the signature position for each signatory.", "templateDynamicPosition.sameName": "The same name field already exists. Please change the name.", "templateDynamicPosition.saveSuccess": "Saved successfully", "templateDynamicPosition.optionsNotEnough.title": "Tip", "templateDynamicPosition.optionsNotEnough.content": "The single checkbox {alias} has less than two options. Please add.", "templateDynamicPosition.optionsNotEnough.confirmBtn": "Got it", "templateDynamicPosition.labelValid.confirmBtnWithTime": "Got it ({num})", "templateDynamicPosition.labelValid.noCloseTip": "Do not close the page until the 5-second countdown is over", "templateDynamicPosition.labelValid.waiting": "The system is checking if the template fields are correct. Please wait for the 5-second countdown to finish before clicking the save button again.", "templateDynamicPosition.noTerm": "There are currently no available terms. Please go to the Corporate Control Center to add them.", "templateDynamicPosition.helpSlider.title": "User Help", "templateDynamicPosition.helpSlider.firstStep": "Step 1: Select the signatory", "templateDynamicPosition.helpSlider.secondStep": "Step 2: Insert the signing location", "templateDynamicPosition.helpSlider.secondStepDetail": "Select the field to be inserted, left-click and click Insert in the document. If you want to modify the text content, you need to modify the pop-up window on the right, otherwise it is considered invalid (Note: the highlighted background color of the business field is not going to appear in the final contract sent).", "templateDynamicPosition.bookmarkSensorTip": "The following fields are not saved in the template to take effect. If you need to use, please add them again. If you no longer need to use them, please click delete.", "templateDynamicPosition.deleteInvalidBookmarkTip": "(Note: Clicking refresh or closing the page in the template may cause failure to save the fields).", "templateDynamicPosition.moreThenTip": "etc {num} bookmarks", "templateDynamicPosition.dataSyncFailTip": "Dynamic template data processing error. Please contact customer service / technical support.", "templateDynamicFieldEdit.receiver": "Recipient", "templateDynamicFieldEdit.info.notice": "Please note that:", "templateDynamicFieldEdit.info.table.0": "Dynamic forms do not allow renaming.", "templateDynamicFieldEdit.info.table.1": "The current dynamic form in the editor is a placeholder, and is displayed by the real number of rows and columns when using the template.", "templateDynamicFieldEdit.info.noTable.0": "If the same business field is added in multiple locations in the template, it only needs to be filled once and will be saved as the same value.", "templateDynamicFieldEdit.info.noTable.1": "Property settings will be synchronized to update all business fields with the same name in the current template.", "authorityApprove.account": "Account:", "authorityApprove.name": "Name", "authorityApprove.sealReselect": "The applicant already has this seal and needs to be reauthorized to sign the contract.", "authorityApprove.electronicSeal": "Select seals", "authorityApprove.admin.title": "Transfer to the main administrator", "authorityApprove.admin.sender": "Transferred by: ", "authorityApprove.admin.powerTitle": "The transferee requests to retain the permissions.", "authorityApprove.admin.detailTitle": "{name} (Account: {applyUserAccount}) is transferring the system administrator role of {entName} to you. The main responsibilities and permissions of the administrator are:", "authorityApprove.admin.detailInfo": "1. Corporate seal use and allocation; | 2. Corporate member management; | 3. Corporate contract management.", "authorityApprove.admin.tip": "The system administrator role is typically held by the company's legal representative, financial manager, legal affairs manager, IT department manager, or business operations manager to ensure effective fulfillment of responsibilities.", "authorityApprove.admin.tip2": "Do you accept to be the company's main administrator?", "authorityApprove.admin.resultSuccess.title": "You have accepted the transfer.", "authorityApprove.admin.resultSuccess.tip": "You have agreed to become a corporate administrator and to authorize the relevant rights and seals.", "authorityApprove.admin.resultFail.title": "You have declined the transfer.", "authorityApprove.admin.resultFail.tip": "You have declined to become a corporate administrator for the following reason: {reason}", "authorityApprove.admin.resultDetail": "Transfer approval details", "authorityApprove.power.title": "Permission approval", "authorityApprove.power.tip": "{apply<PERSON><PERSON><PERSON><PERSON>} is a non-corporate member, and the person will be added as a corporate member automatically after agreeing to the permissions.", "authorityApprove.power.applyUserName": "Applicant's name", "authorityApprove.power.powerTitle": "Apply for permissions", "authorityApprove.power.resultSuccess.title": "You have completed the authorization.", "authorityApprove.power.resultSuccess.tip": "The applicant will be granted partial access and functionality to complete the contract.", "authorityApprove.power.resultFail.title": "You have rejected the application.", "authorityApprove.power.resultFail.tip": "The applicant will receive your response and adjust the application based on your comments: {reason}.", "authorityApprove.power.resultDetail": "Application approval details", "authorityApprove.preAuth.applyUserName": "Transferred by: ", "authorityApprove.preAuth.title": "Corporate real name authentication", "authorityApprove.preAuth.powerTitle": "Application permissions:", "authorityApprove.powerTitleTip": "Contracts requesting viewing permissions include pending contracts without specified signer accounts or contracts where other accounts claim the designated signing account.", "authorityApprove.preAuth.presetConfiguration": "Preset configuration", "authorityApprove.preAuth.realName": "Corporate authentication", "authorityApprove.preAuth.step1": "The first step: Please preset the seal style", "authorityApprove.preAuth.step2": "The second step: Transfer the authenticated people's permission settings", "authorityApprove.preAuth.applyInfo.t1": "Users who apply to you for corporate authentication", "authorityApprove.preAuth.applyInfo.t2": "Name of the company applying for authentication", "authorityApprove.preAuth.signInfo": "You need to sign the {contractAlias} {contractTitle} from the sender {contractSenderName}.", "authorityApprove.preAuth.seal.c1": "Method 1: Use the default system style", "authorityApprove.preAuth.seal.c2": "Method 2: Upload a picture to create a corporate seal", "authorityApprove.preAuth.tip": "If {apply<PERSON><PERSON><PERSON><PERSON>} is a non-corporate member, the person will be automatically added as a corporate member after agreeing to the permissions.", "authorityApprove.preAuth.resultDetail": "Application details", "authorityApprove.viewEntContract": "View Corporate Contracts", "authorityApprove.contractLimit": "Contract Scope：", "authorityApprove.entSeal": "Sign Company Contract（Electronic Company Seal）", "authorityApprove.entSealTooltip": "After obtaining the seal, all companies within the signing authority scope can use this seal for signing.", "authorityApprove.getMore": "Get More Permissions", "authorityApprove.distributionSeal": "Assign Seal", "moreRightDialog.moreRight": "More permissions", "moreRightDialog.confirmBtn": "Determine", "moreRightDialog.viewContract": "View Company Contracts", "moreRightDialog.nowContract": "Current contracts", "moreRightDialog.nowSenderProxyContract2": "Contracts sent by {contractSenderName}", "moreRightDialog.allSenderProxyContract": "All Contracts", "moreRightDialog.proxyContractTip": "Contracts requesting viewing permissions include pending contracts without specified signer accounts or contracts where other accounts claim the designated signing account.", "moreRightDialog.signContract": "Sign corporate contracts ({sealName})", "moreRightDialog.nowSenderContract": "Contracts sent by the current sender", "moreRightDialog.nowSenderContract2": "Contracts sent by {contractSenderName}", "moreRightDialog.allSenderContract": "All Contracts", "moreRightDialog.noApplySignRight": "I don't sign, and I don't need a stamp.", "moreRightDialog.canSignTip": "Signing is allowed within the viewable range.", "moreRightDialog.allTip": "Note: The current sender includes the company, its group and subsidiaries, and business divisions.", "counterDialog.longTime": "Contracts are being sent, which may take", "counterDialog.close": "Got it", "counterDialog.minutes": "minutes", "timingSend.individualApprovalLimit": "Each company flexibly configures the approval process，You cannot use the 'scheduled send' function", "timingSend.hasNoTimingSendFeature": "Your company has not yet enabled this function.", "timingSend.approveTopTip": "Timed approval: The first reviewer in the order of \"review before sending\" receives the contract at the following time:", "timingSend.approveBottomTip": "The time when the next reviewer receives the contract is the time when the previous reviewer approves it.", "timingSend.signTopTip": "Timed signing: Select the time when the first signatory receives the contract: | Timed signing: Select the time when the signatory receives the contract: | Timed signing: Select the time when the signatory receives the contract.", "timingSend.signBottomTip1": "If the sending time set is earlier than the review completion time, it will be sent immediately after the review is completed.", "timingSend.signBottomTip2": "The next signatory receives the contract at the time when the previous signatory's signature is completed.", "timingSend.signBottomTip3": "If the first signatory's company has set \"review before signing\", the time is when the reviewer receives the contract. The signatory will not receive the contract until after the review. | If the first signatory's company has set \"review before signing\", the time is when the reviewer receives the contract. The signatory will not receive the contract until after the review.", "timingSend.receiveNow": "Receive the contract immediately after it is sent successfully.", "timingSend.previousDay": "The day before", "timingSend.nextDay": "The day after", "timingSend.timeLimitTip1": "Timed signing cannot be earlier than timed review.", "timingSend.timeLimitTip2": "Timed sending shall be set at least 15 minutes after the current time.", "autoSignDialog.waitTip": "Tips", "autoSignDialog.errorTip": "Reasons for auto sign failure", "autoSignDialog.title": "Reason for auto sign failure", "autoSignDialog.reason": "You cannot sign this contract using the auto sign function because the following conditions are not met:", "autoSignDialog.wait": "Auto sign is being prepared.", "tagManage.deleteDone": "Successfully deleted", "tagManage.setDone": "Successful setup", "tagManage.noEditPermission": "You do not have the permission to configure the contract fulfillment reminder.", "tagManage.addLabel": "Add label", "tagManage.delLabel": "Remove label", "tagManage.afterDelLblTip": "After remove, tags on the contract will disappear. ", "tagManage.delLblAA": "Remove labels”{name}“", "tagManage.confirm": "confirm", "tagManage.cancel": "Cancel", "tagManage.slotTip1": "The configuration rules are in effect for contracts in the current folder and do not affect the configuration in the Corporate Control Center", "tagManage.slotTip2": "Folder-to-folder configuration rules also do not affect each other.", "tagManage.noTagTip": "Corporate Control Center - No labels have been set in the Corporate Control Center ", "tagManage.addTag": "Adding labels", "tagManage.updateTip": "The label on the contract will be updated to:", "tagManage.noUseLabel": "No label", "tagManage.confirmTitle": "Tip", "tagManage.send": "Contract sent by:", "settingDownloadFileName.settingTitle": "Set contract download name", "settingDownloadFileName.defaultName": "System default file name", "settingDownloadFileName.titleAndId": "(Contract name + contract ID)", "settingDownloadFileName.defineName": "Custom file name", "settingDownloadFileName.defineNameTip": "（maximum 5）", "settingDownloadFileName.limitNumTip": "请至少选择一个自定义文件名", "settingDownloadFileName.noInputValueTip": "请输入自定义文件名", "settingDownloadFileName.hasIllegalCharacter": "自定义文件名中含有非法字符", "settingDownloadFileName.inputPlaceholder": "Please input file name", "settingDownloadFileName.contractTitle": "Contract name", "settingDownloadFileName.contractId": "contract ID", "settingDownloadFileName.receiver": "Receiver", "settingDownloadFileName.personOrEntName": "(name of individual , enterprise)", "settingDownloadFileName.hasSelectedCase": "Selected combinations:", "settingDownloadFileName.noRemindBtn": "Don't prompt again during login", "settingDownloadFileName.tip": "注：采用私有存储方式的合同定义的文件名称将无法生效", "pageTitle.doc.docList": "合同管理", "pageTitle.doc.docDetail": "合同详情", "pageTitle.doc.docExport": "合同导出", "pageTitle.doc.docView": "合同预览", "pageTitle.doc.sealApply": "申请用印", "pageTitle.doc.authIntercept": "个人实名拦截", "pageTitle.doc.entAuthIntercept": "企业实名拦截", "pageTitle.doc.applyJoinEnt": "申请加入企业", "pageTitle.doc.batchLog": "批量操作结果", "pageTitle.doc.rejectSigner": "驳回重签", "pageTitle.dynamicTemplate.set": "设置模板", "pageTitle.dynamicTemplate.preview": "动态模板效果预览", "pageTitle.entDoc.manage": "履约文件夹管理", "pageTitle.entDoc.permission": "权限管理", "pageTitle.entDoc.list": "企业履约管理", "pageTitle.entDoc.detail": "企业合同详情", "pageTitle.send.prepare": "选择模板", "pageTitle.send.send": "发送合同", "pageTitle.send.batchImport": "批量发送合同", "pageTitle.send.inputField": "填写发件方业务字段", "pageTitle.permission.confirm": "Permission confirmation", "pageTitle.permission.apply": "Permission application", "pageTitle.template.list": "模板列表", "pageTitle.template.childList": "模板子列表", "pageTitle.template.detail": "模板详情", "pageTitle.template.permission": "权限管理", "pageTitle.template.config": "设置模板", "pageTitle.template.approval": "Template approve", "transferContract.transferContract": "移交", "transferContract.contractTransfer": "移交", "transferContract.tip": "提示", "transferContract.confirmTip": "移交后，将不再由您持有，您的合同管理列表中无法搜索到该份合同。确定移交吗?", "transferContract.accountNameTip": "接收人姓名与账号不匹配，请确保接收人信息真实准确。", "transferContract.noJoinEntTip": "该接收人账号未加入企业，移交后对方有可能无法完成签署，是否确定移交。", "transferContract.success": "移交成功", "transferContract.resultInfo": "已成功移交给{receiverName}（{receiverAccount}），他将收到签署通知。", "transferContract.verifyCodeTip1": "您可通过查验码或立即跳转小程序。", "transferContract.verifyCodeTip2": "查看签署进度。查看后，「上上签查合同」小程序将为您保留查验记录。", "transferContract.downloadVerifyCode": "下载查验码", "transferContract.receiverAccount": "接收人账号", "transferContract.receiverName": "接收人姓名", "transferContract.receiverAccountPlaceholder": "请输入接收人手机号或邮箱", "transferContract.accountErrorTip": "请输入正确的账号", "transferContract.receiverNamePlaceholder": "请输入接收人真实姓名", "transferContract.transferNamePlaceholder": "请输入您的姓名", "transferContract.transferName": "移交人姓名", "transferContract.transferAccount": "移交人账号", "transferContract.transferTip": "请注意接收人账号务必填写正确，如若移交错误，需要联系企业主管理员将合同重新移交给正确签署人。", "transferContract.adminAccount": "账号：{entAdminAccount}", "transferContract.adminName": "企业昵称：{entAdminName}", "transferContract.admin": "主管理员", "transferContract.confirmTransfer": "确认移交", "transferContract.pleaseInputReceiverInfo": "请填写接收人信息：", "transferContract.pleaseInputTransferInfo": "请填写移交人信息：", "transferContract.name": "姓名：", "transferContract.account": "账号：", "transferContract.transfer": "移交", "transferContract.cancel": "取消", "ssoSendDialog.title": "提示", "ssoSendDialog.main": "请确认，您将以个人身份发送合同?", "ssoSendDialog.tip": "如需开展公司业务，请点击右上角，切换至对应的企业主体后，再发送合同。", "ssoSendDialog.confirm": "去切换至企业", "ssoSendDialog.cancel": "使用个人主体", "contractComparison.translateBtn": "合同翻译", "contractComparison.reapplytn": "Request for Stamp", "contractComparison.waitforStamp": "The contract is awaiting {person} ({account}) to stamp it. Should the person responsible for stamping be changed?", "contractComparison.comparisonBtn": "Comparison", "contractComparison.extractionBtn": "Extraction", "contractComparison.documentSelect": "Please upload the file you need to {type}.Support only Word and PDF file", "contractComparison.noPermission": "This is an advanced function, please contact your special signing employee or call the customer service phone (************) to open.", "contractComparison.prompt": "Prompt", "contractComparison.sendedContract": "Sender's contract", "contractComparison.uploadedContract": "Your uploaded contract documents", "contractComparison.comparisonResult": "Compare results", "contractComparison.differences": "({num} differences in total)", "contractComparison.pageNum": "Page {page}", "contractComparison.difference": "Difference {num}", "contractComparison.uploadTitle": "Upload the files that you want to compare", "contractComparison.uploadError": "Support only Word and PDF file", "contractComparison.download": "Download the contract", "contractComparison.log.title": "Contract comparison record", "contractComparison.log.detail": "Detail", "contractComparison.log.comparing": "Comparing...", "contractComparison.log.refresh": "Refresh", "contractComparison.log.download": "Download comparison results", "contractComparison.log.toDetail": "View comparison details", "contractComparison.doCompare": "compare", "contractComparison.doTranslate": "translate", "contractComparison.doExtract": "extract", "safeBox.guideTitle": "合同保险柜", "safeBox.guideTitleTip": "保密合同，只有合同持有人（参与合同的人）才能查看", "safeBox.howToUse": "如何使用保险柜？", "safeBox.useGuide": "查看使用说明", "safeBox.step1": "第一步：前往企业控制台-角色管理页，为角色勾选合同权限里的的合同保密项。", "safeBox.step2": "第二步：拥有权限后，在合同详情页可以将合同移入保险柜。", "safeBox.step3": "第三步：保险柜内的保密合同在合同管理页会带有“秘”图标。", "safeBox.hasNoPermission": "合同已被移入保险柜，仅合同持有人（参与合同的人）可以查看", "safeBox.noSafeBoxPermission": "此合同为保密合同，只有合同持有人才能查看；您可以联系合同持有人将合同恢复为正常合同后再查看", "safeBox.hideInfoPlaceholder": "保险柜隐藏数据", "safeBox.receiverTip": "参与了合同，含发送、审批、签署、补全以及被抄送的本企业/集团成员账号", "safeBox.entFolderTip": "通过履约文件夹分享后，可以被更多人查看，请注意是否有泄密风险", "safeBox.view": "查看", "safeBox.safeBox": "保险柜", "lang": "en", "customsCheck.customsStandardCheck": "Customs standard detection", "customsCheck.checking": "Customs document standard detection in progress, please wait...", "customsCheck.customsFontRequire": "Customs font requirements", "customsCheck.customsFontUse": "Please use one or more of the following 14 standard fonts", "customsCheck.customsCheckResult": "Customs document detection results", "customsCheck.fileNameRequire": "The file name is longer than 64 characters or 32 Chinese characters", "customsCheck.fontErr": "The document font does not meet the Customs requirements. The current font is | ,Please check the Customs font requirements and use the required font", "customsCheck.checkFontRequirement": "Check the Customs font requirements", "customsCheck.emptyPageErr": "There is a blank page in the file. Please delete it first and then send，A blank page is page | ", "customsCheck.checkStandard": "Detection content according to standard", "customsCheck.checkInGov": "It shall be subject to the actual signing results in the customs system", "customsCheck.nameMulErr": "File name length detection (within 64 characters or 32 Chinese characters)", "customsCheck.emptyMulErr": "Document blank page detection (no blank pages allowed in the document)", "customsCheck.checkFont": "Document font detection", "customsCheck.notSatisfy": "do not meet the requirements", "customsCheck.useCustomsFont": "Please use the required font", "customsCheck.checkSuccess": "Detection passed", "customsCheck.checkPass": "Detection passed", "customsCheck.document": "document", "docContentTableCol.noReceiver": "No recipients added", "docSlider.check2017ContractTip.title": "tips", "docSlider.check2017ContractTip.msg": "Please view contracts created before 2017 in the newly opened window", "docSlider.check2017ContractTip.confirm": "Ok", "docSlider.check2017Contract": "View contracts before 2017", "docSlider.all": "All", "docSlider.unarchive": "Not filed", "docSlider.archived": "Filed", "docSlider.shareToMemberTips.title": "You are sharing to: all members of {name}", "docSlider.shareToMemberTips.share": "Share", "docSlider.shareToMemberTips.confirm": "Confirm", "docSlider.shareToMemberTips.cancel": "Cancel", "docSlider.deleteFolderTips.title": "After deleting, the contracts in the folder will be moved to \"All Contracts\". Do you confirm?", "docSlider.deleteFolderTips.delete": "Delete", "docSlider.deleteFolderTips.confirm": "Confirm", "docSlider.deleteFolderTips.cancel": "Cancel", "docSlider.shareToMeFolder": "Folder shared by others", "docSlider.sharedFolder": "Shared folder", "docSlider.cancelShare": "Cancel sharing", "docSlider.syncEntFolder": "Sync to fulfillment folder", "docSlider.stopSyncEntFolder": "Stop syncing to the fulfillment folder", "docSlider.share": "Share", "docSlider.initSign": "Start now", "docSlider.otherSource": "View other source files", "docSlider.otherSourceTip": "View other source files Click to view the contract, certificate file, and V2 contract signed by the interface platform.", "docSlider.toggleSearchBar": "Displays the search \\ label bar", "docSlider.toggleSearchBar1": "Currently displaying search bar/label bar", "docSlider.toggleSearchBar2": "Currently hide search bar/label bar", "docSlider.fastOperate": "Quick views", "docSlider.operateStatus.signing": "Signing", "docSlider.operateStatus.needMeOperate": "Need me to operate", "docSlider.operateStatus.inApproval": "Sub-judice…", "docSlider.operateStatus.needOthersSign": "Need  others to sign", "docSlider.operateStatus.closing": "Signing is about to close", "docSlider.operateStatus.signComplete": "Signing complete", "docSlider.operateStatus.closed": "Stop signed the contract", "docSlider.allDocs": "All documents", "docSlider.allDocsType.inbox": "Inbox", "docSlider.allDocsType.outbox": "Outbox", "docSlider.allDocsType.closing": "Contract is about to expire", "docSlider.allDocsType.closed": "Contract has expired", "docSlider.allDocsType.draft": "Draft", "docSlider.folder": "Folder", "docSlider.newFolder": "New folder", "docSlider.rename": "<PERSON><PERSON>", "docSlider.delete": "Delete", "docSlider.enterpriseFolder": "Business folder (contract type)", "docSlider.enterpriseFilingContract": "Business filing contract", "docSlider.noAuthority": "No new contract management entry permissions", "docSlider.noAuthorityTips.info": "After activation, you will experience the function of \"Company Archived Contract\" management, which helps you to manage contracts efficiently.", "docSlider.noAuthorityTips.toDetail": "More details: ", "docSlider.noAuthorityTips.cancel": "Cancel", "docSlider.noAuthorityTips.open": "Open", "docSlider.assignAuthority": "Please contact the administrator to assign you permission", "docSlider.msgBox.tips": "Prompt", "docSlider.msgBox.info": "Please view \"other source files\" in the newly opened window page.", "docSlider.msgBox.confirm": "Understood", "docSlider.addFolder": "Add folder", "docSlider.switchEntFail": "Failed to switch enterprise archive contract, please try again later!", "docSlider.openFail": "Failed to open", "docSlider.openLater": "Failed to open, please try again later!", "docSlider.notPublicUser": "This user is not a public cloud user", "docSlider.notEntUser": "This user is not a corporate user", "docSlider.cantReadEntFolder": "This user cannot view the enterprise folder (contract type)", "docSlider.stick": "Place at the top", "docSlider.autoArchive": "Auto archive", "docSlider.performanceManage": "Contract performance", "docContentTop.searchTitlePlaceholder": "Contract Name / Sender Name / Sender Name", "docContentTop.moreSearch": "More search", "docContentTop.output": "Export", "docContentTop.import": "Import", "docContentTop.allContracts": "All contracts", "docContentTop.listConfig": "List config", "docContentTop.filterList.signStatus": "Contract status", "docContentTop.filterList.archiveFolders": "Folder", "docContentTop.filterList.contractType": "Contract type", "docContentTop.filterList.sharedByMe": "I shared", "docContentTop.filterList.sharedToMe": "Shared", "docContentTop.filterList.allFiles": "All files", "docContentTop.companyTree": "Organization", "docContentTop.unmovedContract": "Unmoved contract", "docContentTop.contractLabelList.labor": "Labor Contract", "docContentTop.contractLabelList.borrow": "Borrow Contract", "docContentTop.contractLabelList.legal": "Legal contract", "docContentTop.contractLabelList.loan": "Loan contract", "docContentTop.contractLabelList.transfer": "Transfer contract", "docContentTop.search.contractNum": "Contract ID", "docContentTop.search.contractNumPlaceholder": "Please enter the contract number", "docContentTop.search.sender": "Sender", "docContentTop.search.senderPlaceholder": "Sender name or account number", "docContentTop.search.receiver": "Recipient", "docContentTop.search.receiverPlaceholder": "Recipient name or account number", "docContentTop.search.sendTime": "Signing initiation time", "docContentTop.search.signDeadline": "Signing deadline", "docContentTop.search.timeStartPlaceholder": "Please select the start time", "docContentTop.search.timeEndPlaceholder": "Please select the end time", "docContentTop.search.source": "Source platform", "docContentTop.search.ssq": "BestSign", "docContentTop.search.search": "Search for", "docContentTop.searchMsg.contractNum": "Please enter the correct contract number", "docContentTop.searchMsg.sender": "Please enter the correct sender name or account number", "docContentTop.searchMsg.receiver": "Please enter the correct recipient name or account number", "docContentTop.all": "All", "docContentTop.signStatus.needMeApproval": "Waiting for my approval", "docContentTop.signStatus.needMeSign": "Waiting for me to sign", "docContentTop.signStatus.inApproval": "Sub-judice…", "docContentTop.signStatus.needOthersSign": "Need  others to sign", "docContentTop.signStatus.signComplete": "Signing complete", "docContentTop.signStatus.signOverdue": "Overdue signing", "docContentTop.signStatus.rejected": "Rejected", "docContentTop.signStatus.revoked": "Revoked", "docContentTop.searchAll": "Select all", "docContentTop.confirm": "OK", "docContentTop.reset": "Reset", "docContentTop.selectRange": "Select date range", "docContentTop.datePicker.weekend": "Last week", "docContentTop.datePicker.month": "Last month", "docContentTop.datePicker.month3": "Last three months", "docContentTop.popover.listStatus": "List status", "docContentTop.popover.reset": "Restore initial status", "docContentTop.popover.showLabel": "Display field", "docContentTop.popover.showLabelOperate": "(Drag and adjust the order, click × delete)", "docContentTop.popover.most": "Up to 50", "docContentTop.popover.hideLabel": "Hidden field", "docContentTop.popover.hideLabelOperate": "(drag to the area above to add)", "docContentTop.popover.confirm": "OK", "docContentTop.popover.cancel": "Cancel", "docContentTable.hideField": "Hide fields", "docContentTable.showField": "Displayed Fields", "docContentTable.notFoundField": "Template field not found", "docContentTable.notFoundFieldTips.0": "If a temporary field with the name \"xxx\" is used in the template and needs to be retrieved, then:", "docContentTable.notFoundFieldTips.1": "In the Corporate Control Center - Field Management - Content Fields page, configure a field with the name \"xxx\" and wait for about 15 minutes for the field \"xxx\" to appear for you to configure.", "docContentTable.userSigned": "The signatory has signed", "docContentTable.userReject": "The signatory has refused to sign", "docContentTable.userNotSign": "The signatory has not signed", "docContentTable.resend": "Resend", "docContentTable.claim": "Claim and sign", "docContentTable.pendingClaim": "待认领", "docContentTable.proxySign": "Proxy signing", "docContentTable.contractMoveToAllFolder": "The contract has been moved to \"All contracts\"", "docContentTable.contractMoveToRecycle": "The contract has been deleted.", "docContentTable.contractRestoreToAllFolder": "The contract has been successfully restored to the [All] folder.", "docContentTable.notAllowDeleteContract": "The contracts are allowed to be deleted in the current folder.", "docContentTable.searchTooMuchResultTip": "Too many search results. Please optimize your search condition.", "docContentTable.cannotMoveToRecycle": "The pending contract does not support being deleted", "docContentTable.moveToRecycleTip": "Delete operation does not affect contract status and operations", "docContentTable.statusIconDesc.draft": "Draft", "docContentTable.statusIconDesc.signning": "Under signing", "docContentTable.statusIconDesc.reject": "The contract is rejected", "docContentTable.statusIconDesc.complete": "Done", "docContentTable.statusIconDesc.revoked": "The contract is cancelled", "docContentTable.statusIconDesc.signOverdue": "The contract is overdue", "docContentTable.statusIconDesc.sendApprove": "Sending is under approval", "docContentTable.statusIconDesc.approveReject": "<PERSON><PERSON><PERSON><PERSON> rejected", "docContentTable.deleteLableSuccess": "Label removed successfully", "docContentTable.clickDeleteLabel": "Click to remove the label", "docContentTable.canNotBatchDownloadTip": "Cannot be downloaded in bulk，You will need to obtain a download code from the contract initiator to download them one by one.", "docContentTable.noCanDownloadContract": "No contracts can be downloaded", "docContentTable.downloadRemainContract": "Download the rest of the contracts", "docContentTable.cancleDownload": "Cancel downloading", "docContentTable.remind": "Remind", "docContentTable.needDownloadCodeTip": "You will need to get a download code from the contract sender to download the contracts one by one.", "docContentTable.needAuthTip": "You need to authenticate your real name or be consistent with your current real name to download the contract.", "docContentTable.revokeContractIdsTip": "The contract has been cancelled and cannot be downloaded.", "docContentTable.canNotDownload": "Cannot be downloaded in bulk .", "docContentTable.partCanNotDownload": "Some contracts cannot be downloaded in bulk", "docContentTable.noCanDeleteContract": "No contract can be deleted", "docContentTable.transferSucess": "Transfer successfully", "docContentTable.transferFailure": "Transfer failed", "docContentTable.notRightToTransferTip": "You don't have the authority to transfer the selected contract. Please select again", "docContentTable.batchSetTag": "Bulk set labels", "docContentTable.setTag": "Set labels", "docContentTable.transfer": "Transfer", "docContentTable.archive": "File", "docContentTable.invalid": "Invalid", "docContentTable.moveOutRecycle": "Rest<PERSON>", "docContentTable.moveToRecycle": "Delete", "docContentTable.changeSigner": "Change signatory", "docContentTable.changeSignerDialog.confirm": "Confirm", "docContentTable.changeSignerDialog.cancel": "Cancel", "docContentTable.changeSignerDialog.title": "Individual signatories who have not signed the contract", "docContentTable.changeSignerDialog.tips": "Individuals who have not had their turn to sign cannot be changed", "docContentTable.changeSignerTable.signerName": "Signer name", "docContentTable.changeSignerTable.idCard": "IDCard", "docContentTable.changeSignerTable.account": "Phone or Email", "docContentTable.changeSignerTable.roleName": "Signer role", "docContentTable.changeSignerOption.must": "Mandatory", "docContentTable.changeSignerOption.optional": "Optional", "docContentTable.changeSignerVerify.invalidSubmit": "There is a format error in the submitted data.", "docContentTable.changeSignerVerify.success": "Save success", "docContentTable.changeSignerVerify.fail": "Save Fail", "docContentTable.changeSignerVerify.invalidIDCard": "ID card format error", "docContentTable.changeSignerVerify.invalidAccount": "Incorrect format of cell phone number/email", "docContentTable.changeSignerVerify.invalidSignerName": "There is a format error in the name", "docContentTable.batchOperateLoading": "The bulk operation is loading", "docContentTable.selectedContracts": "{num} files selected", "docContentTable.operationTips": "请先确定业务线后再继续操作", "docContentTable.batchBtn.sign": "Batch signing", "docContentTable.batchBtn.approval": "Batch approval", "docContentTable.batchBtn.remind": "Batch reminder", "docContentTable.batchBtn.revoke": "Bulk revocation", "docContentTable.batchBtn.download": "Batch download", "docContentTable.batchBtn.delete": "batch deletion", "docContentTable.batchBtn.move": "Batch move", "docContentTable.operate": "operating", "docContentTable.searchNull": "No such contract found", "docContentTable.toDetail": "Jump details", "docContentTable.download": "Download", "docContentTable.move": "Move", "docContentTable.reSend": "Re-send", "docContentTable.moveCancel": "<PERSON><PERSON> move", "docContentTable.signer": "Signatory", "docContentTable.status": "Status", "docContentTable.sendDate": "Date of delivery", "docContentTable.deadline": "Signing deadline", "docContentTable.contractStatus.needMeSign": "Need me to sign", "docContentTable.contractStatus.needMeApproval": "Need my approval", "docContentTable.contractStatus.inApproval": "Sub-judice…", "docContentTable.contractStatus.needOthersSign": "Need  others to sign", "docContentTable.contractStatus.signComplete": "Signing complete", "docContentTable.contractStatus.draft": "Draft", "docContentTable.contractStatus.signOverdue": "Overdue signing", "docContentTable.contractStatus.rejected": "Rejected", "docContentTable.contractStatus.revoked": "Revoked", "docContentTable.contractStatus.beRejected": "Refusal", "docContentTable.contractStatus.deadline": "Deadline", "docContentTable.contractStatus.invalid": "Invalidated", "docContentTable.signStatus": "Contract status", "docContentTable.catchMap.download": "Download", "docContentTable.catchMap.reject": "Reject", "docContentTable.catchMap.revoke": "Cancel", "docContentTable.catchMap.delete": "Delete", "docContentTable.catchMap.cantOperate": "Unable to {operate} contract", "docContentTable.catchMap.hybridNetHeader": "The sender's enterprise uses the contract private storage method, but the current network cannot connect to the sender's contract storage server.", "docContentTable.catchMap.hybridNetMsg": "I suggest you check if the network has been connected to the sender's intranet and try again.", "docContentTable.catchMap.checkNet": "Please check if the network is connected to the intranet.", "docContentTable.confirm": "OK", "docContentTable.cancel": "Cancel", "docContentTable.continue": "Continue to download", "docContentTable.next": "Continue", "docContentTable.delete": "Remove", "docContentTable.searchAll": "Select all", "docContentTable.nullToSign": "No signable documents", "docContentTable.nullToApproval": "No approvable documents", "docContentTable.nullToRemind": "No reminder document", "docContentTable.nullToRevoke": "Irrevocable document", "docContentTable.sign": "Sign", "docContentTable.approval": "Approve", "docContentTable.remindSucc": "Remind success", "docContentTable.remindFail": "The following {errorSum} contract batch reminder failed:", "docContentTable.notice": "Prompt", "docContentTable.revoke": "Cancel", "docContentTable.revokeReason": "Reason for batch withdrawal", "docContentTable.batchDownloadTip.msg1": "The sender of the following contract uses the contract private storage, but the current network cannot connect to the sender's contract storage server", "docContentTable.batchDownloadTip.msg2": "The name of the contract that cannot be downloaded is as follows:", "docContentTable.deleteSucc": "Delete successfully ", "docContentTable.giveAuthor": "Please contact the administrator to assign you permission", "docContentTable.view": "View", "docContentTable.paperSign": "The paper signed", "docContentTable.onLineSign": "Online signature", "docContentTable.usePaperSign": "To enable the paper signed", "docContentTable.modifyExpires": "Modify contract expiration date", "noBizLineDoc.title": "未指定业务线（或接收人尚未加入企业）的合同", "noBizLineDoc.confirm": "确认", "noBizLineDoc.cancel": "取消", "noBizLineDoc.noBizLineTip": "You have {number} contracts without designated business lines (or the recipient has not joined the company), please click here to handle", "docDialog.searchName": "Search Name", "docDialog.inputSearchName": "Please enter a common search name", "docDialog.commonSearch": "Common search", "docDialog.saveCommonSearch": "Save as common search", "docDialog.setSearchName": "Set common search name", "docDialog.setSearchNameTip": "Set the name for the recently saved common search", "docDialog.autoSetListConfitTip": "When using, the list configuration (list header) will be updated according to the search term for this time", "docDialog.searchNameNotEmpty": "The commonly used search name cannot be empty", "docDialog.lestSearchContentTip": "At least one search content needs to be filled in", "docDialog.deleteSearchContent": "Do you want to delete this commonly used search", "docDialog.maxSetCommonSearchAmout": "Up to 10 common searches can be set", "docDialog.approvalOpinion.title": "Batch approval opinions", "docDialog.approvalOpinion.yes": "Pass", "docDialog.approvalOpinion.no": "Rejected", "docDialog.approvalOpinion.result": "The results of approval:", "docDialog.approvalOpinion.fillInContent": "Please fill in the approval opinion:", "docDialog.approvalOpinion.placeHolder": "Optional, 255 characters maximum", "docDialog.notice": "Prompt", "docDialog.ok": "Understood", "docDialog.inputSenderPersonAndCompany": "Please fill in individual/company name", "docDialog.inputReceiverPersonAndCompany": "Please fill in individual/company name", "docDialog.contractLblSearch": "Search contract label ", "docDialog.putAway": "Collapse", "docDialog.unfold": "Expand", "docDialog.searchFor": "Search", "docDialog.contractInvolveTip": "Individual or company members involving the contract", "docDialog.plsSelect": "Please select", "docDialog.moreBusinessFields": "More (business fields)", "docDialog.confirm": "Confirm", "docDialog.plsSelectData": "Please select a date", "docDialog.addShortcutEntrance": "Create a new shortcut", "docDialog.reStoreState": "Restore to the initial status", "docDialog.showEntrance": "Show the entrance", "docDialog.hideEntrance": "Hide the entrance", "docDialog.shortcutManage": "Quick views management", "docDialog.plsInputShortcutName": "Please put in the shortcut name", "docDialog.plsChooseLable": "Please select the contract label", "docDialog.plsChooseSignStatus": "Please select the operating status", "docDialog.plsInputRoleTip": "Please fill in the business role", "docDialog.plsInputInfoTip": "The condition information is incomplete. Please add", "docDialog.accountFormatErrorTip": "Please enter the correct cell phone number or email addres", "docDialog.conditionTypeText.APPROVER": "Reviewer", "docDialog.conditionTypeText.ROLE": "Signatory role", "docDialog.conditionTypeText.SIGNER": "Signatory's account", "docDialog.conditionTypeText.SENDER": "Initiator", "docDialog.ifOrEqual": "If|equal", "docDialog.finish": "Done", "docDialog.search": "Search", "docDialog.meetAllConditiionTip": "Contracts meeting the above conditions can be found at the shortcut", "docDialog.plsChooseLblName": "Please select the label name", "docDialog.ifLableContain": "If the contract label contains", "docDialog.defineAsLable": "Define by the label", "docDialog.addCondition": "New conditions", "docDialog.plsInputPlaceholder": "Please enter the business role|Please enter email / cell phone number", "docDialog.signStatusIsTip": "and the \"operating status\" is", "docDialog.roleEquilTip": "If the \"business role\" is", "docDialog.codition": "Conditions", "docDialog.defineRoleTip": "Customize filtering of contracts based on contracting roles or the accounts and actions of contract signatories, reviewers, and contract initiators in your organization.", "docDialog.defineByStatus": "define by operating status", "docDialog.back": "Return", "docDialog.chooseIdentity": "Select your identity", "docDialog.plsSelectTime": "Please select a time", "docDialog.noMoreThan365dayTip": "day（no more than 365 days）", "docDialog.noMoreThan365day": "no more than 365 days", "docDialog.customize": "Customize", "docDialog.beforeNumDay": "{num}days in advance", "docDialog.belowContractOperteFail": "Operation of the following contract {tip} failed：", "docDialog.plsInput": "Please put in", "docDialog.archive": "filed", "docDialog.goSign": "Go to sign", "docDialog.num": "contracts", "docDialog.otherNeedSignTip": "Other contracts to be signed", "docDialog.selectedContracts": "{num} contracts selected", "docDialog.needClaimContract": "Contracts need to be claimed", "docDialog.batchContainClaimHint": "Some of the contracts you have selected need to be claimed first. Please check if these contracts should be signed by you!", "docDialog.batchDowLoadHint": "The contract you selected needs to be downloaded in batches:", "docDialog.labelNotAddTips": "The label {num} cannot be attached to the following contracts:", "docDialog.batchDownloadTips": "The contract you choose is stored in a different storage server and you need to download the contract in batches. Please click the button below to complete the contract download.", "docDialog.batchDownload": "Batch download", "docDialog.saver": "contract depositor", "docDialog.ssq": "BestSign", "docDialog.cancel": "Cancel", "docDialog.delete": "Delete", "docDialog.deleteTips": "Files in this folder will not be deleted together", "docDialog.deleteConfirm": "Are you sure you want to delete this folder?", "docDialog.delLabel": "Delete tag", "docDialog.folderPlaceholder": "Please enter a folder name", "docDialog.addFolder": "Add folder", "docDialog.addLabel": "Add tag", "docDialog.dividedLabel": "Multiple labels separated and used by cart return", "docDialog.myLabels": "My tag", "docDialog.label": "label {num}", "docDialog.max10": "Add up to 10 labels", "docDialog.length10": "Labels up to 10 characters long", "docDialog.labelExist": "Label already exists", "docDialog.selectMember": "Select member", "docDialog.batchBtn.sign": "Bulk signing", "docDialog.batchBtn.approval": "Bulk approval", "docDialog.batchBtn.remind": "Bulk reminder", "docDialog.batchBtn.revoke": "Bulk revocation", "docDialog.batchSignTips": "Contracts that require you to sign and do not need to fill in the content can be signed in batches", "docDialog.supportBatch": "The following contract supports batch {type}", "docDialog.remindSucc": "Remind success", "docDialog.remindFail": "The following {errorSum} contract batch reminder failed:", "docDialog.remind": "Prompt", "docDialog._confirm": "OK", "docDialog._cancel": "Cancel", "docDialog.revoke": "Cancel", "docDialog.exportDetail": " export contract details ", "docDialog.lessThan365": "Initiation interval cannot exceed 365 days at most", "docDialog.narrowRange": "The number of contracts exceeds {maxNum}, please shorten the time range.", "docDialog.lessThan2000": "Do not export more than {maxNum} items in a single order", "docDialog.sendTime": "Signing initiation time", "docDialog.signDeadline": "Signing deadline", "docDialog.timeStartPlaceholder": "Select start time", "docDialog.to": "to", "docDialog.timeEndPlaceholder": "Select end time", "docDialog.signStatus": "Contract status", "docDialog.noFileExport": "No file can be exported", "docDialog.downloading": "Please wait patiently during download", "docDialog.move": "Move", "docDialog.folder": "Folder", "docDialog.openFuzzyMatch": "Currently in exact search mode. Click to switch to fuzzy search.", "docDialog.closeFuzzyMatch": "Currently in fuzzy search mode. Click to enable an exact search (only contracts with the same keywords  will be searched).", "docDialog.noFolder": "No folders yet", "docDialog.fileSucc": "Successfully archived", "docBatch.agree": "Agree", "docBatch.reject": "Reject", "docBatch.approve": "Approval", "docBatch.approving": "Reviewing...", "docBatch.approved": "Review success", "docBatch.approveAgree": "Approval result: Approved", "docBatch.approveReject": "Approval result: Rejected", "docBatch.approveSuggest": "Approval comments", "docBatch.canNotInput": "Optional", "docBatch.confirm1": "Confirm", "docBatch.cancel1": "Cancel", "docBatch.inputSignPsw": "Please enter the signing password", "docBatch.inputSixDigitalNum": "Please enter a 6-digit number", "docBatch.changeEmailVerify": "Switch to Email Verification", "docBatch.changePhoneVerify": "Switch to Phone Verification", "docBatch.batchApproveSuccess": "Bulk approve successfully", "docBatch.belowContractApporveFail": "Bulk approve failed of the following {length} contracts", "docBatch.tips": "Tips", "docBatch.confirm": "Confirm", "docBatch.signVerify": "Signing verification", "docBatch.signPswLockedTip": "The password is locked and will be unlocked automatically after 3 hours. You can also", "docBatch.findBackPsw": "retrieve your password", "docBatch.toUnlockNow": "to unlock immediately", "docBatch.signPswCanEnterTip": "Signing password incorrect, you can enter for another 2/1 times, do you", "docBatch.forgetPsw": "forget password", "docBatch.verifyFormatError": "Verification code format error", "docBatch.signPassFormatError": "The signing password format is wrong", "docBatch.networkTimeoutTip": "Network timed out, please refresh the page and check the contract signing status", "docBatch.batchSignSuccess": "Bulk sign successfully", "docBatch.belowContractSignFail": "Failed to bulk sign of the following {length} contracts", "docBatch.signWithNameTip": "The contract is currently signed by {name}", "docBatch.useElectronicSeal": "Use e-stamp", "docBatch.noWantUseTheSealTip": "Don't want to use this stamp? You can change the stamp after real name authentication", "docBatch.toAuth": "Go for real name authentication", "docBatch.useSignature": "Use signature", "docBatch.signatureFinishTip": "The signing has been completed, you can also go to finish the real name authentication, so that your signature will be better legally protected", "docBatch.toUseSignatureTip": "Please click on \"signature\" on the left to sign. You can also use real name first for more legal protection of your signature. ", "docBatch.batchSign": "Bulk sign", "docBatch.batchApprove": "Bulk approve", "docBatch.ssqNotReviewDiffTip": "BestSign is not responsible for reviewing the content differences between the current version and the effective version of the contract. By using the bulk signing function, you agree to sign the effective version of the following contracts.", "docBatch.chooseElectronicSeal": "Select a stamp", "docBatch.fileNumTip": "{num} files ", "docBatch.totalFileNumTip": "{num} documents in total", "docBatch.chooseDefaultSignature": "Select default signature", "docBatch.more": "More", "docBatch.sign": "sign", "docBatch.file": "Ducuments", "docBatch.signature": "Signature", "docBatch.dataErrorTip": "Data error, please try again!", "docBatch.noCanSignContract": "No contract to sign", "docBatch.noCanApproveContract": "No contract to approve", "docBatch.noReceiver": "No recipients added", "docBatch.label": "label", "docView.totalPageTip": "Page {num}, {total} pages in total", "docView.numOfPage": "Number of pages", "docView.page": "Page", "docView.canNotCheckContract": "The contract cannot be viewed", "docView.privateStoreContractTip": "The sender uses private storage for the contract, but the current network cannot connect to the contract storage server", "docExport.exportRecord": "Record of export", "docExport.refresh": "Refresh", "docExport.every1000ContractWaitingTime": "The waiting time is about half a minute per 1000 pieces of contract details", "docExport.fileName": "Document name", "docExport.createTime": "Time of creation", "docExport.chooseTimePeriod": "Select time period", "docExport.to": "to", "docExport.contractStatus": "Contract status", "docExport.operate": "operate", "docExport.download": "download", "docExport.expired": "overdue", "docExport.waitExport": "Waiting for export", "batchSearch.add": "Batch import", "batchSearch.batchAddOption": "Batch add option", "batchSearch.batchAddTip": "By default, you can add one contract in a row, so please fill in each row in turn.", "batchSearch.batchAddPlaceHolder": "Please enter the content", "batchSearch.name": "Bulk search", "batchSearch.viewContract": "View contracts", "batchSearch.selectSearchItem": "Search content", "batchSearch.itemSelectTip": "Please select search terms", "batchSearch.searchContent": "Bulk input (Each search term takes up one line, and with no more than 20 lines)", "batchSearch.searchContentLineLimitTip": "If the search term selected is a contract number or internal business number, up to 100 lines are supported.", "batchSearch.searchContentPlaceholder": "Please enter search terms", "batchSearch.searchConditions": "Search records: {num} rows", "batchSearch.contractsTotal": "Number of contracts：{count} copies", "batchSearch.unfindConditions": "Records not searched: {num} rows", "batchSearch.contractNums": "Number of contracts", "batchSearch.order": "Serial number", "batchSearch.searchContentLengthLimitTip": "Support up to 100 keywords, otherwise it cannot be processed", "batchSearch.inputCorrectFieldName": "Please select the correct search criteria (select the search criteria from the drop down box).", "docDetail.crossPlatformSign": "Cross-platform signing", "docDetail.platformSign.ja": "Japanese Contracting Party", "docDetail.platformSign.zh": "Chinese Contracting Party", "docDetail.specialContract": "特殊合同", "docDetail.specialContractTip": "跨企业转交的特殊合同，只能查看，不要做接口调用、下载、作废、重新发送、出证，否则可能出现异常", "docDetail.tips": "Tips", "docDetail.confirm": "Confirm", "docDetail.noNeedSign": "你当前身份代表了{name}，但该身份已完成签署或无需签署。建议你退出后以其他身份重新进入重试", "docDetail.signRole": "(contracted roles)", "docDetail.signRoleTextOnly": "Contracted role:", "docDetail.scanCodeSign": "QR Code Sign", "docDetail.other": "Other", "docDetail.shareSignLink": "Share the signing link", "docDetail.messageAndFaceVerify": "Face scan + verification code to sign", "docDetail.faceSign": "Sign with facial verification", "docDetail.faceFirstVerifyCodeSecond": "Priority sign with facial verification, alternate sign with SMS code verification", "docDetail.contractRecipient": "Contract recipient", "docDetail.inputReceiver": "Recipient", "docDetail.inputReceiverTip": "The name of the handler filled in by the sender. Only visible to the sending company (including group subsidiaries).", "docDetail.inputReceiverNotInput": "Not filled in", "docDetail.personalOperateLog": "Individual contract operation log", "docDetail.recordDialog.date": "Date", "docDetail.recordDialog.user": "User", "docDetail.recordDialog.operate": "Operation", "docDetail.recordDialog.view": "View", "docDetail.recordDialog.download": "Download", "docDetail.remarks": "Remarks", "docDetail.operateRecords": "Record of operation", "docDetail.borrowingRecords": "Contract borrowing records", "docDetail.currentHolder": "Current holder", "docDetail.currentEnterprise": "Company under the current account", "docDetail.companyInterOperationLog": "Internal operation log of the company", "docDetail.companyInterOperationLogTip": "Internal operation log of the company is only visible to the respective organization.", "docDetail.receiverMap.sender": "Contract sender", "docDetail.receiverMap.signer": "Contract signatory", "docDetail.receiverMap.ccUser": "Contract Cc", "docDetail.receiverMap.editor": "Contract editor", "docDetail.noRead": "Unread", "docDetail.read": "Read", "docDetail.downloadCode": "Contract download code", "docDetail.noTagToAddHint": "No labels yet, please go to Company Console to add", "docDetail.noSender": "You are not the contract sender, so you cannot set tags for this contract", "docDetail.noOpenFeature": "The sending company of this contract has not yet activated the function to set tags", "docDetail.requireFieldNotAllowEmpty": "Required fields cannot be empty", "docDetail.modifySuccess": "Modify successfully", "docDetail.uncategorized": "Unclassified", "docDetail.notAllowModifyContractType": "The contract type is not allowed to be modified when the contract is under {type}", "docDetail.notAllowModifyContractTypeBackup": "{type}中的合同不允许修改合同类型（备用）", "docDetail.setTag": "Set labels", "docDetail.contractTag": "Contract labels", "docDetail.plsInput": "please put in", "docDetail.plsInputCompanyInternalNum": "please enter the Internal business number", "docDetail.companyInternalNum": "Internal business number", "docDetail.plsSelect": "please select", "docDetail.modify": "Modify", "docDetail.contractDetailInfo": "Contract details", "docDetail.contractDetailInfoTip": "Contract details are only visible to the sending company (including group subsidiaries).", "docDetail.viewDetail": "View details", "docDetail.slideContentTip.downloadFile": "Download source file", "docDetail.slideContentTip.look": "View", "docDetail.slideContentTip.signNotice": "Signing instructions", "docDetail.slideContentTip.contractAncillaryInformation": "Attachments to the contract", "docDetail.slideContentTip.content": "Content", "docDetail.slideContentTip.document": "Document", "docDetail.slideContentTip.compressedFile": "压缩文件", "docDetail.slideContentTip.supplementsTitle": "Supplemental agreement for {account}", "docDetail.slideContentTip.fromCurrentSupplements": "Supplementary agreements from the current template.", "docDetail.slideContentTip.fromOtherSupplements": "Supplemental agreements from other templates.", "docDetail.slideContentTip.supplementsContractId": "Contract ID:", "docDetail.slideContentTip.supplementsContractTitle": "Contract title:", "docDetail.slideContentTip.moreSupplementsLine": "View more", "docDetail.slideContentTip.updateTime": "Update Time", "docDetail.slideContentTip.updateTimeTip": "Before the contract is signed, the sender can update the signing instructions.", "docDetail.slideContentTip.updatePrivateBtn": "Update", "docDetail.boxCollection": "Contract signatory information", "docDetail.downloadDepositConfirmTip.title": "The appendix of Contract you downloaded is a non-sensitive information version, with private information hidden and not applicable for court proceedings. If you need to use it for court proceedings, please contact us for the full version.", "docDetail.downloadDepositConfirmTip.hint": "tips", "docDetail.downloadDepositConfirmTip.confrim": "Continue to download", "docDetail.downloadDepositConfirmTip.cancel": "Cancel", "docDetail.downloadTip.versionTip": "Please select the desired version to download:", "docDetail.downloadTip.normalVersion": "Normal Version", "docDetail.downloadTip.highLightVersion": "Approval special edition: All \"template content fields\" in the contract are highlighted to facilitate the capture of key information.", "docDetail.downloadTip.title": "As the contract is not yet completed, you are downloading a preview file of the contract that is not yet in effect.", "docDetail.downloadTip.invalidTitle": "As the contract is not invalid, you are downloading a preview file of the contract that is not yet in effect.", "docDetail.downloadTip.hint": "tips", "docDetail.downloadTip.confirm": "confirm", "docDetail.downloadTip.cancel": "cancel", "docDetail.transferFail": "Transfer failed", "docDetail.transferSuccessGoManagePage": "Transfer successfully and return to the contract management page", "docDetail.claimSign": "Claim and sign", "docDetail.downloadDepositPageTip": "Download appendix of Contract (non-sensitive information version)", "docDetail.downloadPageTip": "Download appendix of Contract", "docDetail.resend": "Resend", "docDetail.claimSuccess": "Adoption successful, pending approval to sign", "docDetail.approved": "Approved", "docDetail.proxySign": "entrusted signing", "docDetail.notPassed": "rejected", "docDetail.approving": "under review", "docDetail.signning": "Signing in progress", "docDetail.notarized": "Notarized", "docDetail.invalid": "Voided", "docDetail.currentFolder": "Current folder", "docDetail.has": "Invalidated", "docDetail.now": "Being Invalidated by:", "docDetail.for": "Invalidating", "docDetail.archive": "Filed contract", "docDetail.deadlineForSigning": "Deadline for signing", "docDetail.endFinishTime": "Signing complete date", "docDetail.contractImportTime": "Contract import time", "docDetail.contractSendTime": "Sending time", "docDetail.moveToSafeBox": "移入保险柜", "docDetail.moveToSafeBoxTip": "合同移入保险柜后，只能由合同持有人（合同参与人）才能查看，主管理员也不能查看。", "docDetail.moveFromSafeBox": "移出保险柜", "docDetail.moveFromSafeBoxTip": "移出保险柜后，合同的管理员能统一查看、管理此合同", "docDetail.moveToSuccess": "移入成功", "docDetail.moveOutSuccess": "移出成功", "docDetail.back": "back", "docDetail.contractInfo": "Contract information", "docDetail.basicInfo": "Basic information", "docDetail.contractNum": "Contract ID", "docDetail.downloadVerifyCode": "Download contract verification code", "docDetail.verifyCode": "Contract verification code", "docDetail.sender": "Sender", "docDetail.personAccount": "Personal account", "docDetail.entAccount": "Enterprise account", "docDetail.operator": "Operator", "docDetail.signStartTime": "Start signing time", "docDetail.signDeadline": "Signing deadline", "docDetail.contractExpireDate": "Contract expiration date", "docDetail.viewInvalidContract": "View Void Statement", "docDetail.labelLimitEditTip": "Your company administrator has set the field \"{name}\" as non-editable. To modify this field value, please go to \"Console - Business Field Management - Contract Description Fields\" page and change it to \"Allow Field Value Modification\".", "docDetail.none": "None", "docDetail.edit": "Modify", "docDetail.settings": "Set up", "docDetail.clear": "Clear", "docDetail.from": "Source", "docDetail.folder": "Folder", "docDetail.contractType": "Contract type", "docDetail.contractTypeBackup": "Contract type (Alternative)", "docDetail.reason": "Reason", "docDetail.sign": "Sign", "docDetail.approval": "approval", "docDetail.viewAttach": "View the attached pages", "docDetail.downloadContract": "Download the document", "docDetail.downloadAttach": "Download the attached page", "docDetail.print": "Print", "docDetail.certificatedTooltip": "The contract and related evidence have been documented in the judicial chain of the Hangzhou Internet Court", "docDetail.needMeSign": "Need me to sign", "docDetail.needMeApproval": "Need my approval", "docDetail.inApproval": "Sub-judice…", "docDetail.needOthersSign": "Need others to sign", "docDetail.signComplete": "Signing complete", "docDetail.signOverdue": "Overdue signing", "docDetail.rejected": "Rejected", "docDetail.revoked": "Revoked", "docDetail.contractCompleteTime": "Signing complete time", "docDetail.contractEndTime": "Signing end time", "docDetail.reject": "Reject", "docDetail.revoke": "Cancel", "docDetail.download": "Download", "docDetail.viewSignOrders": "Signing order diagram", "docDetail.viewApprovalProcess": "View approval process", "docDetail.viewApprovalAnnotate": "查看审批批注", "docDetail.completed": "Signing completed", "docDetail.cc": "Cc", "docDetail.ccer": "Copy party", "docDetail.signer": "Receiver", "docDetail.signSubject": "Signatory", "docDetail.signSubjectTooltip": "The signing subject filled in by the sender is", "docDetail.user": "User", "docDetail.IDNumber": "ID number", "docDetail.state": "Status", "docDetail.time": "Signing time", "docDetail.notice": "Remind", "docDetail.detail": "Details", "docDetail.RealNameCertificationRequired": "Real-name certification required", "docDetail.RealNameCertificationNotRequired": "No real name certification required", "docDetail.MustHandwrittenSignature": "Must sign with handwritten signature", "docDetail.handWritingRecognition": "Handwriting recognition", "docDetail.handWritingRecognitionChangeTip1": "1. You must have \"View Contract\" permission, as well as \"Send Contract\" and \"Adjust Signatory Requirements\" permissions for the corresponding contract template.", "docDetail.handWritingRecognitionChangeTip2": "2. You can only modify this configuration when the current signatory has not completed signing.", "docDetail.privateMessage": "Message", "docDetail.attachment": "Attachment", "docDetail.rejectReason": "Reason", "docDetail.notSigned": "Not signed", "docDetail.notViewed": "Not viewed", "docDetail.viewed": "Viewed", "docDetail.signed": "Signed", "docDetail.viewedNotSigned": "<PERSON>, not signed", "docDetail.notApproval": "Unapproved", "docDetail.remindSucceed": "Reminder message sent", "docDetail.reviewDetails": "Approval details", "docDetail.close": "Shut Down", "docDetail.entInnerOperateDetail": "Internal operation details", "docDetail.approve": "Agree", "docDetail.disapprove": "Reject", "docDetail.applySeal": "Application for printing", "docDetail.applied": "Already applied", "docDetail.apply": "Application", "docDetail.toOtherSign": "Transfer to someone else to sign", "docDetail.handOver": "Transfer", "docDetail.approvalOpinions": "Approval comments", "docDetail.useSeal": "Print", "docDetail.signature": "Signature", "docDetail.use": "Use", "docDetail.date": "Date", "docDetail.fill": "Fil in", "docDetail.times": "Secondary", "docDetail.place": "Place", "docDetail.contractDetail": "Contract details", "docDetail.viewMore": "View more", "docDetail.collapse": "Fold", "docDetail.WXLinkTitle": "Mini Program Link：", "docDetail.signLinkTitle": "Web Link：", "docDetail.signLink": "Signing a link", "docDetail.saveQRCode": "Save the QR code or copy the link and share it with the signatory", "docDetail.saveWXCode": "Save the Program code or copy the link and share it with the signatory", "docDetail.changeQRCode": "Switch QR Code", "docDetail.changeWXCode": "Switching Program Code", "docDetail.signQRCode": "Sign the link QR code", "docDetail.showlinkDec": "{senderName} has sent you \"{contractName}\". Click the link to view: {url}", "docDetail.copyUrl": "Copy Link", "docDetail.copy": "Copy", "docDetail.copySucc": "Successful copy", "docDetail.copyFail": "Replication copy", "docDetail.certified": "Certified", "docDetail.unCertified": "Uncertified", "docDetail.claimed": "Already claimed", "docDetail.unSort": "Not sorted", "docDetail.signPage": "Page: {page}", "docDetail.handWriteNotAllowed": "Handwritten signatures are not allowed", "docDetail.entSign": "Signature", "docDetail.stamp": "Stamp", "docDetail.stampSign": "Stamp and signature", "docDetail.requireEnterIdentityAssurance": "Enable verification of the operator's identity.", "docDetail.noNeedToSign": "No longer need to sign", "docDetail.requestSeal": "Business verification seal", "docDetail.requestSealAgree": "Conformity seal", "docDetail.requestSealRefuse": "Non-conforming seal", "docDetail.requestSealRemark": "Remarks for non-conforming seal", "docDetail.viewContentInfo": "View contract content field", "docDetail.contractContentInfo": "Contract content field", "docDetail.contentDate": "date", "docDetail.combobox": "Drop down box", "docDetail.number": "Number", "docDetail.text": "text", "docDetail.notFilled": "Not filled here", "docDetail.radio": "Single choice", "docDetail.checkbox": "Multiple choice", "docDetail.filledBySender": "To be filled in by the sender", "docDetail.filledBy": "To be filled by {writer}", "docDetail.empty": "The result is empty", "docDetail.changeToPaperSign": "Switched to paper signing", "docDetail.templateNum": "Template ID", "docDetail.QRCode": "Contract QR Code", "docDetail.templateTip": "If the contract does not use a template, the template number will be 0 by default", "docDetail.changeContractStatusForPaperSign": "Modify status", "docDetail.changeContractStatus": "Extend signing time", "docDetail.changeContractStatusConfirm.title": "Extend signing time", "docDetail.changeContractStatusConfirm.tip.0": "Change the contract status to \"Signed\", and the unsigned party will be marked as \"Signed on paper\".", "docDetail.changeContractStatusConfirm.tip.1": "Please click OK after you receive the original paper copy with seal.',点击确定。 ", "docDetail.changeContractStatusConfirm.confirm": "Confirm", "docDetail.changeContractStatusConfirm.cancel": "Cancel", "docDetail.hasUsePaperSign": "(Paper signature is enabled)", "docDetail.signedAging": "Timeframe for signing", "docDetail.completeContractInfo": "补全合同信息", "docDetail.day": "day", "docDetail.hour": "hour", "docDetail.minute": "minutes", "docDetail.accountChangeTip": "The user account has been changed from “{previous}” to “{current}”", "docDetail.byEncryptionSign": "通过加密签署方式签署", "docDetail.password": "密码", "docDetail.byTwoFactorSign": "通过二要素校验签署", "docDetail.completeByRole": "待签约角色{roleName}补全", "docDetail.completedByRole": "（由签约角色{roleName}补全）", "docDetail.completeSuccess": "补全成功", "docDetail.completeInfoFillErrorTip": "信息填写不规范，请检查页面上的报错说明（一般为红色），按提示修改后重试", "docDetail.viewContractOfSameCode": "按照\"公司内部编号\"快速查找", "docDetail.contractTitle": "合同标题：", "docDetail.contractId": "合同编号：", "docDetail.privateTip": "Only the signatory and the sending company can view.", "shortcut.all": "All contracts", "shortcut.sign": "Need me to sign", "shortcut.approve": "Need me to approve", "chooseEntFolder.title": "Select Corporate Contract Fulfillment Management System", "chooseEntFolder.desc": "Please select the corporate fulfillment management system you need to access.", "syncEntFolder.title": "Sync to the fulfillment folder", "syncEntFolder.createFolder": "Create a new folder", "exportContract.title": "Scope of export", "exportContract.content1": "The scope of the export is the details of all contracts in the current search results (the detail fields are the same as the table headers of the contract list).", "exportContract.content2": "Do you want the export results to include the subcontracts of the multi-document contract?", "exportContract.radioText.0": "yes", "exportContract.radioText.1": "no", "exportContract.cancel": "Cancel", "exportContract.export": "Start exporting", "scanCodeRemind.tip": "Tip", "scanCodeRemind.confirm": "Confirm", "scanCodeRemind.content": "If the contract contains a signatory using code to sign, please download the verification code from the details page of the contract and send it to notify.", "scanCodeRemind.detailContent": "If the signatory uses code to sign, please download the contract verification code and send it to notify.", "crossplatformList.notice": "You have {number} cross-platform signing contracts, please click here to handle", "crossplatformList.breadcrumb": "Cross-platform signing contracts", "crossplatformList.viewDetail": "View details", "consts.shortcutMap.myFolders": "My contract fulfillment folder", "consts.shortcutMap.allFolders": "All contract fulfillment folders", "consts.contractStatus.all": "All states", "consts.contractStatus.needMeSign": "Waiting for me to sign", "consts.contractStatus.needMeApproval": "Waiting for my approval", "consts.contractStatus.inApproval": "Sub-judice…", "consts.contractStatus.needOthersSign": "Need  others to sign", "consts.contractStatus.signComplete": "Signing complete", "consts.contractStatus.signOverdue": "Overdue signing", "consts.contractStatus.rejected": "Rejected", "consts.contractStatus.revoked": "Revoked", "consts.contractStatus.invalid": "Voided", "consts.rejectReasonList.signOperateReason": "If you have questions about the signing operation/verification operation, further communication is needed", "consts.rejectReasonList.termReason": "I have doubts about the terms/content of the contract and need to communicate further", "consts.rejectReasonList.explainReason": "If you are not aware of the contract contents, please inform us in advance", "consts.rejectReasonList.otherReason": "Others (please fill in the reason)", "consts.templatePermissionMap.sendContract": "Send the contract", "consts.templatePermissionMap.modifyDocument": "Modify the document", "consts.templatePermissionMap.modifyReceiver": "Modify the signing party", "consts.templatePermissionMap.addCCReceiver": "Add Cc", "consts.templatePermissionMap.modifySignRequirement": "Modify signing requirements", "consts.templatePermissionMap.dragSignLabel": "Move the signature field (including the place of signature)", "consts.templatePermissionMap.modifySignLabel": "Add/delete signature fields (including the place of signature)", "consts.templatePermissionMap.editable": "Edit the template", "consts.templatePermissionMap.templateDuplicate": "Duplicate the template", "consts.templatePermissionMap.modifyDocumentFederation": "Set up the document portfolio", "consts.templatePermissionMap.invalidStatement": "Set Void Declaration", "consts.templatePermissionMap.grantManage": "Permission assignment", "consts.templatePermissionMap.editCustomScene": "Scene config", "consts.templatePermissionMap.setDefaultValue": "Set default values", "consts.templatePermissionMap.templateSpecialSeal": "Template seal", "consts.templatePermissionMap.editSupplyAgree": "Supplementary agreements", "consts.templatePermissionMap.contractConfidentiality": "Contract confidentiality", "consts.templatePermissionMap.stampRecommend": "AI agent configuration", "consts.templatePermissionDesc.useTmp.name": "Permission to use templates", "consts.templatePermissionDesc.useTmp.tabName": "Use of templates", "consts.templatePermissionDesc.useTmp.sendContract": "You are allowed to send a contract using the current template. Additional permissions for template use can only be granted on the basis of this permission", "consts.templatePermissionDesc.useTmp.modifyDocument": "When sending a contract using a template, you are allowed to upload a new contract document or contract attachment, and you are allowed to temporarily delete an uploaded document from the template", "consts.templatePermissionDesc.useTmp.modifyReceiver": "When sending a contract using a template, you are allowed to modify the account number and name of a contracting party, and add or remove contracting parties (to be used in conjunction with the 'add signature field' permission)", "consts.templatePermissionDesc.useTmp.modifySignRequirement": "When sending a contract using a template, you are allowed to modify the signature requirements.", "consts.templatePermissionDesc.useTmp.dragSignLabel": "When sending a contract using a template, you can change the places for stamps, signature, and business fields", "consts.templatePermissionDesc.useTmp.modifySignLabel": "When sending a contract using a template, you are allowed to add places for new stamps, signatures, and business fields.", "consts.templatePermissionDesc.useTmp.setDefaultValue": "When sending the contract, you can write the initial value of the field for the signatory.", "consts.templatePermissionDesc.useTmp.addCCReceiver": "Only the account number of Cc is allowed to be added when sending a contract using a template.", "consts.templatePermissionDesc.manageTmp.name": "Template management permission", "consts.templatePermissionDesc.manageTmp.tabName": "Manage templates", "consts.templatePermissionDesc.manageTmp.editable": "Editing of templates is allowed by clicking the \"Edit\" button on the contract template list page. It also supports deleting templates,enabling/disabling templates and download templates.", "consts.templatePermissionDesc.manageTmp.templateDuplicate": "You are allowed to copy templates by clicking the 'copy' button on the contract template list page", "consts.templatePermissionDesc.manageTmp.modifyDocumentFederation": "You are allowed to add, delete, and modify document combinations in the template document list page", "consts.templatePermissionDesc.manageTmp.modifySceneConfig": "You are allowed to config scene item in template detail page", "consts.templatePermissionDesc.manageTmp.invalidStatement": "How to cancel a signed template contract", "consts.templatePermissionDesc.manageTmp.editSupplyAgree": "Set the associated template of the current template, so that the contract sent by the associated template to the same signatory can become a supplementary agreement to the contract of the current template.", "consts.templatePermissionDesc.manageTmp.contractConfidentiality": "It can be configured that the contracts associated with the contract participants (sending, signing, approving and completing) of the group/company can be automatically transferred to a fixed account", "consts.templatePermissionDesc.manageTmp.stampRecommend": "Allow enabling AI agents. AI agents can replace manual operations at specific stages of the electronic signature process.", "consts.templatePermissionDesc.manageGrant.name": "Permission to manage permissions", "consts.templatePermissionDesc.manageGrant.tabName": "Manage permissions", "consts.templatePermissionDesc.manageGrant.grantManage": "You are allowed to authorize the template to others by clicking 'member authorization' or 'role authorization' on the contract template list page.", "consts.contractAlias.doc": "document", "consts.contractAlias.letter": "Confirmation Letter", "consts.contractAlias.proof": "proof of authorizing", "consts.contractAlias.contract": "contract", "consts.contractAlias.agreement": "Consent Agreement", "consts.contractAlias.service_report": "Service Report", "consts.entFolderPermissionMap.contractBorrow": "Borrow contracts to read", "consts.entFolderPermissionMap.borrowApproval": "Modify the approver for borrowing to read", "consts.entFolderPermissionMap.viewList": "View the list details", "consts.entFolderPermissionMap.downloadList": "Export list details", "consts.entFolderPermissionMap.viewContract": "View contract fulfillment details page", "consts.entFolderPermissionMap.downloadContract": "Download contracts", "consts.entFolderPermissionMap.invalidContract": "Voiding a contract", "consts.entFolderPermissionMap.resendContract": "Resend contracts", "consts.entFolderPermissionMap.setTag": "Set labels", "consts.entFolderPermissionMap.distriButtonPermission": "Transfer and permission assignment", "consts.entFolderPermissionMap.syncHronize": "Personal folder synchronization", "consts.entFolderPermissionMap.remove": "Delete contracts", "consts.entFolderPermissionMap.group": "Grouping of contracts", "consts.entFolderPermissionMap.edit": "Modify folders", "consts.entFolderPermissionMap.folderManageMaterial": "Manage contract fulfillment documents", "consts.entFolderPermissionMap.fulfillMaterialTip": "You can set labels on the contract fulfillment details page, and you can upload or delete files generated during contract fulfillment.", "consts.crossFormHeader.contractTitle": "Contract title", "consts.crossFormHeader.sender": "Sender", "consts.crossFormHeader.receiver": "Recipient", "consts.crossFormHeader.contractStatus": "Contract status", "consts.crossFormHeader.contractId": "Contract ID", "consts.crossFormHeader.sendTime": "Sent date", "consts.crossFormHeader.expireTime": "Contract expiration date", "consts.crossFormHeader.signDeadlineTime": "Signing deadline", "consts.crossFormHeader.finishTime": "Signing completion time", "hybridBusiness.isCheckingNet": "Checking the hybrid cloud network environment", "mixin.createSuccessful": "Created successfully", "mixin.setLabel": "Set label", "recoverSpecialSeal.title": "The seal cannot be used.", "recoverSpecialSeal.description1": "The sender requires you to use the seal in order to sign the contract, but your company has deleted the seal. To ensure that the signing goes smoothly, please restore the seal to the administrator.", "recoverSpecialSeal.description2": "If the seal is not suitable for further use, you may contact the sender to revise the seal design and then sign the contract.", "recoverSpecialSeal.postRecover": "Request for restoration of the seal", "recoverSpecialSeal.note": "After you click on it, the administrator will receive an SMS/email requesting the restoration of the seal, and the request will also be visible on the seal management page.", "recoverSpecialSeal.requestSend": "The application for restoration is submitted successfully.", "certificationRenewalDialog.renewalTitle": "Digital certificate renewal", "certificationRenewalDialog.renewalTip": "Your certificate has expired, please renew it in time to avoid document signing", "certificationRenewalDialog.previousIdentity": "Subject holding the certificate:", "certificationRenewalDialog.previousCA": "Original certification authority: ", "certificationRenewalDialog.previousExpiryDate": "Validity period of the original certificate:", "certificationRenewalDialog.previousId": "Original certificate serial number:", "certificationRenewalDialog.renewal": "Agree to renew", "pdf.previewFail": "File preview failed", "pdf.pager": "Page {x}，{y} in total", "pdf.parseFailed": "Failed to parse the pdf file, please click \"OK\" to try again", "pdf.confirm": "Confirm", "tagManage.title": "Set label", "importOffLineDoc.importDoc": "Import contracts", "importOffLineDoc.step0Title": "Step 1: Confirm the name of the company", "importOffLineDoc.step1Title": "Step 2: Upload Excel file", "importOffLineDoc.step2Title": "Step 3: Upload the contract file", "importOffLineDoc.step1Info": "Please download the Excel template first and fill in before importing, the number of imported contracts should not exceed 1000.", "importOffLineDoc.next": "Next", "importOffLineDoc.entName": "Company Name", "importOffLineDoc.archiveFolder": "Folder", "importOffLineDoc.downloadExcel": "Download Excel", "importOffLineDoc.uploadExcel": "Upload Excel", "importOffLineDoc.reUploadExcel": "Re-upload", "importOffLineDoc.step2Info.0": "1. Contract file format can only be PDF or image；", "importOffLineDoc.step2Info.1": "2. After placing all contract files in a folder, compress the folder into a zip (no more than 150M)；", "importOffLineDoc.step2Info.2": "3. The file name including the file extension (such as .pdf) should be consistent with the file name in Excel in the second step；", "importOffLineDoc.uploadZip": "Click to upload zip", "importOffLineDoc.reUploadZip": "Re-upload Zip", "importOffLineDoc.done": "confirm", "importOffLineDoc.back": "Back", "importOffLineDoc.contractTitle": "Contract name", "importOffLineDoc.singerAccount": "Signer account", "importOffLineDoc.singerName": "Signatory name", "importOffLineDoc.uploadSucTip": "Upload is successful, click \"OK\" button to start importing", "importOffLineDoc.outbox": "Outbox", "importOffLineDoc.fileLessThan": "Please upload files smaller than {num}M", "importOffLineDoc.fileTypeValid": "Only {type} format files can be uploaded!", "download.contactGetDownloadCodeTip": "Please contact the contract sender to get the download code, or try to log in to your business system to download.", "download.downloadCode": "Download code", "download.hint": "tips", "download.download": "download", "download.plsInput": "please put in", "download.plsInputDownloadCode": "Please enter the download code", "download.downloadCodeError": "Wrong download code", "download.noAuthDownload": "You need to complete personal real name authentication or keep your real name information consistent to download the contract.", "download.noAuthView": "You need to complete personal authentication or keep your real name information consistent to view contract details.", "download.allFiles": "all files", "download.cancel": "cancel", "download.plsSelectFiles": "Please select a document first", "download.publicCloudDownloadTip": "The contract to be downloaded contains other information submitted by the signatory. Do you want to download it together with the contract?", "download.hybridCloudDownloadTip": "The contract to be downloaded contains other information submitted  by the signatory. ", "download.sameTimeDownloadAttachTip": "Download other attached information together with the contract", "download.downloadContract": "Download contract", "download.downloadAttach": "Download attached information of the contract ", "transfer.list1": "List 1", "transfer.list2": "List 2", "transfer.maxSelectNum": "Maximum field limit of {maxLength} exceeded. Please remove {balance} fields", "poperCascader.plsSelect": "Please select", "poperCascader.person": "People", "poperCascader.selectNumTip": "{A}/{B} of {C} selected", "poperCascader.allSelect": "Select all", "approvalDetail.submitter": "Approval initiator", "approvalDetail.signatory": "Signatory", "approvalDetail.reviewSchedule": "Approval schedule", "approvalDetail.downloadFile": "Download source file", "approvalDetail.content": "content", "approvalDetail.document": "document", "approvalDialog.stepTip.0": "Please select the review process for the contract", "approvalDialog.stepTip.1": "After approval, the contract will be sent automatically", "approvalDialog.stepTip.2": "After approval, we receive the contract", "approvalDialog.back": "Back", "approvalDialog.next.0": "Next", "approvalDialog.next.1": "Submit for approval", "approvalDialog.ourApprovalBeforeSign": "Approval flow before we sign the contract", "approvalDialog.chooseApprover": "Select approver:", "approvalDialog.completeApprovalFlow": "The approval process you submitted is incomplete, please complete and resubmit", "approvalDialog.completeTargetEntApprovalFlow": "The approval process for {entName} that you submitted is incomplete. Please complete it and resubmit", "approvalDialog.viewPrivateLetter": "View messages", "approvalDialog.addPrivateLetter": "Add messages", "approvalDialog.useEntFlow": "Use approval of {name}", "approvalDialog.beforeSendApprove": "Approval before sending", "approvalDialog.beforeSignApprove": "Approval before signing", "approvalDialog.beforeSignApproveTip": "If the signing party does not include this company/its subsidiary, pre-signature approval will not be triggered.", "approvalDialog.contractTypeFlow": "Set approval process for {contractTypeName} (Contract type)：", "approvalDialog.errorTip": "Please select an approval process first", "approvalDialog.useGroupApproval": "Unified use of the group headquarter's approval.", "approvalDialog.setApprovalIndividual": "Each company flexibly configures the approval process.", "approvalDialog.errorTipWithEntName": "Please select the approval process for {entName}", "approvalDialog.noWorkflow": "The contracts sent by this enterprise do not require approval.", "cancelContract.confirm": "confirm", "cancelContract.selectRejectReason": "Please choose the reason for rejection", "cancelContract.reasonWriteTip": "Please fill in the reason for rejection", "cancelContract.refuseReasonOther": "More reasons for rejection (optional) | More reasons for rejection (required)", "cancelContract.inputRejectReason": "Fill in the reason for the refusal to help the other party understand your problem and speed up the contract process", "cancelContract.reject": "Reject", "cancelContract.pwdWrong": "The signing password is wrong, you can also enter 2/1 times, is it", "cancelContract.forgetPwd": "Forget?", "cancelContract.pwdLocked": "Password is locked, after 3 hours you can automatically unlock. You can also pass", "cancelContract.retrievePwd": "Retrieve password", "cancelContract.unlock": "To unlock now", "cancelContract.inputReason": "Please enter {type} reason (may not fill)", "cancelContract.revokeTips": "After withdrawal, the contract recipient cannot view the contract content, but can view the reason for withdrawal. | If the contract has not been received by the recipient (e.g. the contract has not been approved for sending, or it is not yet the turn for the contract to be signed), then the contract will not be visible at all.", "cancelContract.revokeReasons": "Contract content needs to be modified | Recipient account needs to be modified | Signer does not need to receive this contract", "cancelContract.revokeOtherReason": "Other", "cancelContract.rejectTips": "This contract cannot be signed after the refusal", "cancelContract.signPwd": "Signing Password", "cancelContract.inputPwd6": "Please enter a 6-digit signing password", "cancelContract.inputPwd": "Please enter the signing password", "cancelContract.inputNumber6": "Please enter 6 digits", "cancelContract.mail": "E-mail", "cancelContract.phone": "Phone", "cancelContract.verify": "SMS Code", "cancelContract.mailVerify": "Pin Code", "cancelContract.otherNotice.1": "Haven't received text messages all the time? Try ", "cancelContract.otherNotice.2": "Voice verification code ", "cancelContract.otherNotice.3": "Or ", "cancelContract.otherNotice.4": "SMS verification code ", "cancelContract.otherNotice.5": "E-mail verification code ", "cancelContract.sendSucc": "Send successfully", "cancelContract.sendInternalErr": "Send time interval is too short ", "cancelContract.getVerifyCode": "Please Get The Verification Code First", "cancelContract.succ": "{type}successful", "cancelContract.refuseConfirmTip": "You have refused to sign this contract for the reason \"{reason}\". Do you want to continue? After confirmation, you will not be able to sign this contract again.", "cancelContract.waitAndThink": "Reconsider", "contractMove.newFolder": "New folder", "contractMove.plsInput": "please put in", "contractMove.archive": "Filed", "contractMove.noFolder": "No folders yet", "contractMove.confirm": "Confirm", "contractMove.cancel": "Cancel", "contractMove.folderPlaceholder": "Please enter a folder name", "contractMove.fileSucc": "Successfully archived", "contractMove.hasMoveTip": "If an archived contract is archived again, the contract will be moved out of the original folder.", "contractMove.howToViewMovedStatus": "How to check if a contract has been archived", "contractsTransferDialog.originOwner": "previous holder", "contractsTransferDialog.contractTransfer": "contract transfer", "contractsTransferDialog.newOwner": "new holder", "contractsTransferDialog.selectedOwner": "Transferred selected", "contractsTransferDialog.transferRelationship": "Please add at least one transfer", "contractsTransferDialog.searchTip": "Support input account / name search", "contractsTransferDialog.confirm": "OK", "contractsTransferDialog.chooseNewOwner": "please select the new holder", "contractsTransferDialog.notCliam": "the contract has not been claimed", "contractsTransferDialog.bizTransfer": "Transfers across business lines", "contractsTransferDialog.innerTransfer": "Transfer within the business line", "contractsTransferDialog.chooseMultiLine": "Select business line", "contractsTransferDialog.chooseAccount": "Select account", "contractsTransferDialog.displayAccount": "Only the first 50 accounts are displayed", "contractsTransferDialog.transferRoles": "You are to transfer contracts for the following roles: ", "contractsTransferDialog.tip": "Tip", "contractsTransferDialog.continue": "Continue", "contractsTransferDialog.know": "Got it", "contractsTransferDialog.cancel": "Cancel", "linkContract.reason.reSend": "Contracts resent", "linkContract.reason.beReSend": "Contracts resent", "linkContract.reason.invalid": "Contracts declared to be void", "linkContract.reason.beInvalid": "Void contracts", "linkContract.reason.paperSign": "Scanned paper copies of the original contract", "linkContract.reason.bePaperSign": "Paper scans", "linkContract.reason.manualLinked": "Manual linking", "linkContract.contractTitle": "contract title", "linkContract.contractNo": "contract No.", "linkContract.linkedDetail": "linked detail", "linkContract.noPermissionTip": "You do not have permission to view the linked contracts", "linkContract.noCanLinked": "Only manually linked contracts can be cancelled.", "linkContract.title": "Related contracts", "linkContract.connectMore": "Relate to", "linkContract.placeholder": "Please enter the master contract number", "linkContract.contractNoLengthTip": "The contract no must be 19 digits.", "linkContract.revoke": "The contract has been cancelled", "linkContract.overdue": "signature overdue", "linkContract.approvalNotPassed": "approval rejected", "linkContract.reject": "the contract has been rejected", "linkContract.signing": "Under signing", "linkContract.complete": "complete", "linkContract.approvaling": "Under approval", "linkContract.disconnect": "disconnect", "linkContract.disconnectSuccess": "disconnect successfully", "linkContract.connectLimit": "Up to 100 contracts can be connected", "linkContract.noFunctionLimit": "This function has not been activated", "linkContract.submit": "OK", "linkContract.cancel": "Cancel", "linkContract.entAccount": "Enterprise account", "linkContract.personAccount": "Personal account", "linkContract.whetherMasterContract": "Is it a master contract?", "qrCodeTab.pleaseScanToHandleWrite": "Please use WeChat or mobile browser to scan the code, handwritten signature on the mobile device", "qrCodeTab.save": "Save", "selectSignType.chooseSignType": "Choose contract method", "selectSignType.useTemplate": "Use template", "selectSignType.useLocalFile": "Upload local file", "signValidation.VerCodeVerify": "Verification code check", "signValidation.QrCodeVerify": "Qr code check", "signValidation.verifyTip": "BestSign is calling your secure digital certificate, you are in a secure signing environment, please rest assured to sign!", "signValidation.verifyAllTip": "BestSign is calling company Digital CA certificate (Certificate Athority) and your personal Digital CA certificate, and you are in a secure signing environment, please be assured to sign.", "signValidation.appScanVerify": "Scan code verification with BestSign APP", "signValidation.downloadBSApp": "Download BestSign APP", "signValidation.scanned": "Code scan successful", "signValidation.confirmInBSApp": "Please confirm the signature in BestSign app", "signValidation.qrCodeExpired": "QR code is no longer valid. Please refresh and try again", "signValidation.appKey": "APP security check", "signValidation.goToScan": "Go scan code", "signValidation.signSuc": "Sign successfully", "signValidation.if": ",", "signValidation.forgetPassword": "Forgot?", "signValidation.signPsw": "Sign Password", "signValidation.signPwdType": "Please enter 6 digits", "signValidation.email": "E-mail", "signValidation.phoneNumber": "Phone", "signValidation.setNotificationInUserCenter": "Please go to the user center to set the notification method", "signValidation.mailVerificationCode": "Pin Code", "signValidation.verificationCode": "SMS Code", "signValidation.msgTip": "Can't review the SMS messages? Try", "signValidation.voiceVerCode": " a voice call", "signValidation.or": "or", "signValidation.SMSVerCode": "SMS verification code", "signValidation.emailVerCode": "E-mail verification code", "signValidation.doNotWantUseVerCode": "Don't want to use verification code", "signValidation.try": "try", "signValidation.goToFaceVerify": "go to face recognition", "signValidation.submit": "OK", "signValidation.SentSuccessfully": "Send successfully!", "signValidation.intervalTip": "Please send again later", "signValidation.signVerification": "Sign", "signValidation.appliedSeal": "Application for seal has been submitted", "signValidation.operationCompleted": "Operation completed", "signValidation.firstStepTip1": "The first step：Sign with facial verification", "signValidation.firstStepTip2": "(for contracts with facial verification signing requirements)", "signValidation.secondStepTip1": "The second step：Sign with SMS verification code", "signValidation.secondStepTip2": "(for contracts with verification code signing requirements)", "signValidation.useVerCode": "Use verification code", "signValidation.useSignPsw": "Use the password for verification", "signValidation.setSignPsw": "Set the signing password verification", "signValidation.inputVerifyCodeTip": "Please enter verification code", "signValidation.tip": "After the setting is completed, the signing password will be first used by default. You may log in to BestSign's e-Signature platform and enter 'User Center' or log in to BestSign app and enter 'Account Management' to change the password.", "signValidation.saveAndReturnSign": "Save and return to sign", "signValidation.signPwdRemind": "Often sign, verification code wait too long, try signing the password?", "signValidation.toSetSignPwd": "To set the signing password", "switchSubject.chooseIdentity": "Select your identity", "switchSubject.chooseMultiLine": "Select business line", "switchSubject.confirm": "Confirm", "switchSubject.cancel": "Cancel", "unverifyConfirmDialog.tip": "Notice", "unverifyConfirmDialog.isGroupProxyAuth": "The current authentication status of the company is group authentication (not finish authentication yet). The recipient of the contract will not be able to identify your identity. It is recommend that you complete the real name authentication of the company first. ", "unverifyConfirmDialog.unverify": "You have not been certified by real name, and the recipient of the contract will not be able to identify you. We recommend that you first perform real-name certification.", "unverifyConfirmDialog.goAuthenticate": "Go to authentication", "unverifyConfirmDialog.theEnterprise": "The enterprise", "unverifyConfirmDialog.you": "you", "unverifyConfirmDialog.enterprise": "Company", "contractQrCodeDialog.selectVerifyCode": "Select verification code", "contractQrCodeDialog.viewVerifyCode": "View style", "contractQrCodeDialog.preview": "Preview", "contractQrCodeDialog.msg.selectVerifyCode": "Please select the verification code first", "contractQrCodeDialog.msg.success": "Changed successfully", "addAttachmentConfig.dialogTitle": "Configure attachment fields", "addAttachmentConfig.fieldInd": "Sequence number", "addAttachmentConfig.fieldName": "Field name", "addAttachmentConfig.fieldType": "Field content", "addAttachmentConfig.fieldRequire": "Is it mandatory to fill", "addAttachmentConfig.lengthLimit": "No more than 20 characters", "addAttachmentConfig.type": "PDF, Word, Excel and picture formats", "addAttachmentConfig.notRequire": "Optional", "addAttachmentConfig.addField": "Newly added fields", "addAttachmentConfig.submit": "Save", "addAttachmentConfig.cancel": "Cancel", "addAttachmentConfig.requireName": "Names yet to be filled in", "addAttachmentConfig.saveSucc": "Saved successfully", "authInfoChange.title": "Real name change detection", "authInfoChange.confirm": "Confirm", "authInfoChange.changeAuth": "Real name update", "authInfoChange.notifyAdmin": "Notify the administrator", "authInfoChange.notifySuccess": "Success", "authInfoChange.operateSuccess": "Success", "authInfoChange.warningTip.tip1": "It is found that the real name information {oldAuthInfo} of your company '{entName}' on BestSign platform is not consistent with the latest information {newAuthInfo} at the Industry and Commerce Bureau. ", "authInfoChange.warningTip.tip2": "To ensure compliance and effectiveness of your signed e-contracts, please use the latest business information to go through real-name authentication again. ", "authInfoChange.warningTip.tip3": "This operation will not affect your current corporate information. ", "authInfoChange.suggestTip.tip1": "If your company has a group structure, please contact your exclusive CSM, or call BestSign's customer service hotline ************ to update information for real-name authentication, after which you can continue to sign.", "authInfoChange.suggestTip.tip2": "By clicking [Notify Administrator{adminInfo}], ", "authInfoChange.suggestTip.tip3": "you can immediately send a notification to the administrator to guide him/her to go through real-name authentication again. You can also notify offline to ensure timely business activities.", "excelScale.setExcelScale": "Set Excel scaling", "excelScale.info": "If you turn on this function, all columns of Excel will be scaled to one page.", "excelScale.on": "Turn on the scaling function", "excelScale.line.tip1": "Enabling the function will take effect for all subsequent uploaded documents and attachments of the template.", "excelScale.line.tip2": "Template settings will be inherited when using the template", "excelScale.saveSucc": "Saved successfully", "excelScale.saveFail": "Failed to save", "internalSignConfig.configTitle.0": "Only for internal document signing scenario.", "internalSignConfig.configTitle.1": "Only for internal document signing scenario.", "internalSignConfig.title": "Only for internal document signing scenario.", "internalSignConfig.tip.0": "This feature is not applicable to formal contracts (such as labor contracts). After enabling this feature, the use of the template will be restricted as follows:", "internalSignConfig.tip.1": "- Only the company's internal member accounts can be added as signatories.", "internalSignConfig.tip.2": "- Signatories can only use the company signature method", "internalSignConfig.checkboxInfo.0": "Allow the signatory to sign without real name authentication(less legal effect than the real name signing). If unchecked this, the real name authentication is required.", "internalSignConfig.checkboxInfo.1": "The signatory is allowed to sign the contract without signing verification. If not checked this, the signature can only be completed by verifying the verification code (or signing password, facial verification, etc.).", "internalSignConfig.checkboxInfo.2": "Allow the signatory to sign the contract without a handwritting signature (default signature allowed). If unchecked this, each signature must be handwritting.", "internalSignConfig.setAliasTip": "Set the external \"contract alias\" of the contract", "internalSignConfig.setAliasInfo": "Go to the template details page - scenario customization. You can create \"contract alias\" for external use.\n", "permissionHeader.hi": "Hello", "permissionHeader.exit": "Exit", "permissionHeader.help": "Help", "permissionHeader.hotline": "Service hotline", "paperSign.title": "Sign paper contracts", "paperSign.stepText.0": "Next step", "paperSign.stepText.1": "Confirm the signature on paper", "paperSign.stepText.2": "Confirm", "paperSign.needUploadFile": "Please upload scanned copies first.", "paperSign.uploadError": "Uploading failed", "paperSign.cancel": "Cancel", "paperSign.downloadPaperFile": "Obtain paper contracts to sign", "paperSign.step0.title": "You need to download and print the contract first, stamp it with seal, and mail it to the sender.", "paperSign.step0.address": "Mailing address", "paperSign.step0.contactName": "Recipient's name:", "paperSign.step0.contactPhone": "Recipient's contact info.:", "paperSign.step0.defaultValue": "Please make offline request to the sender.", "paperSign.step1.title0": "Step 1: Download & print the paper contract", "paperSign.step1.title0Desc.0": "The downloaded and printed contract should contain the electronic seal pattern. Please", "paperSign.step1.title0Desc.1": "Obtain paper contracts to sign", "paperSign.step1.title1": "Step 2: Stamping", "paperSign.step1.title1Desc": "Stamp the paper contract with the valid corporate seal. ", "paperSign.step1.title2.0": "Step 3:", "paperSign.step1.title2.1": "Upload the scanned documents,", "paperSign.step1.title2.2": "Fill in the verification code to sign on paper documents.", "paperSign.step1.title2Desc.0": "Scan the paper contracts (into PDF files) and upload them.", "paperSign.step1.title2Desc.1": "Then fill in the verification code to complete the signing. The electronic contracts will not show your seal pattern, but will record your operation process.", "paperSign.step2.title.0": "Upload scanned copies of the paper contracts (PDF files)", "paperSign.step2.title.1": "Please confirm that the paper contracts have been downloaded and signed, then click the OK button to complete the paper contract signing process.", "paperSign.step2.uploadFile": "Upload a scanned copy", "paperSign.step2.getCodeVerify": "Verify contract signatures ", "paperSign.step2.isUploading": "Uploading in progress...", "paperSign.operationCompleted": "Operation completed", "changeSignDeadline.title": "Modify contract status", "changeSignDeadline.desc": "After clicking OK, the following changes will occur:", "changeSignDeadline.changeTime": "Change the contract status from \"Overdue\" to \"Signing\" and change the contract signing deadline to: {signDeadline}", "changeSignDeadline.changeTimeTip": "The \"Signing Deadline\" set for the signatory will no longer be in effect", "changeSignDeadline.choose": "You can choose to:", "changeSignDeadline.checkSignNotice": "Push a signing reminder to accounts that have not signed the contracts", "changeSignDeadline.confirmChange": "Do you want to confirm the contract status change?", "changeSignDeadline.cancel": "Cancel", "changeSignDeadline.confirm": "Confirm", "changeSignDeadline.signDeadlineTip": "Each contract can only be extended a maximum of three times.", "addEntFolder.title": "Create new performance folder", "addEntFolder.ent": "Affiliated companies", "addEntFolder.entHolder": "Please select", "addEntFolder.folderName": "Contract fulfillment folder name", "addEntFolder.folderNameHolder": "No more than 20 characters and must be unique, mandatory", "addEntFolder.mark": "Note", "addEntFolder.markHolder": "No more than 100 characters, optional", "addEntFolder.cancel": "Cancel", "addEntFolder.confirm": "Confirm", "addEntFolder.folderNameTip": "The contract fulfillment folder name cannot be empty!", "entContractDownload.failTitle": "The following document download failed", "entContractDownload.documentId": "Document ID", "entContractDownload.documentTitle": "Document Name", "entContractDownload.failReason": "Reason for Failure", "fileLimit.fileLessThan": "Please upload files less than {num}M", "fileLimit.beExcel": "Please upload Excel file", "fileLimit.beAttachmentFile": "Please upload Pdf, Word, Excel files or pictures", "fileLimit.usePdf": "Please upload PDF files or images", "fileLimit.beZip": "Use Zip,7z files when uploading", "fileLimit.fileNameMoreThan": "The name is longer than {num}, and automatically shortened for you", "regs.entNameMaxLength": "Corporate names cannot exceed 60 characters", "unPermissionRemind.title": "Permission denied", "unPermissionRemind.content.noAuth.1": "The name of the signatory designated by the sender should be: {receiverEntName}", "unPermissionRemind.content.noAuth.2": "It is inconsistent with your company name", "unPermissionRemind.content.noAuth.3": "1.Please complete the authentication of {receiverEntName} with clicking the “Authentication” button in the below;", "unPermissionRemind.content.noAuth.4": "2.If completed the authentication, please check the company name. If the names are inconsistent, please contact the sender or BestSign for confirmation", "unPermissionRemind.content.noJoin": "The contract is sent by {senderEntName} to the signatory {receiverEntName}, which means that you must sign with the company seal of the signatory. You have not joined the company account or you have no permission to use the company seal of the signatory. You can apply for the permission or transfer this contract to other user who has the permission to sign.", "unPermissionRemind.content.noView": "The contract is sent by {senderEntName} to the signatory {receiverEntName}. You do not have the permission to view the information. You need to apply.", "unPermissionRemind.content.noSign": "The contract is sent by {senderEntName} to the signatory {receiverEntName}, which needs to be signed with the enterprise electronic seal. You have no signing permission, you need to apply", "unPermissionRemind.transferTip": "You can also transfer the contract to the person who has the authority of the seal for signature. We will inform you after the transfer and signature is successful", "unPermissionRemind.transferBtn": "Transfer the contract >>", "unPermissionRemind.giveOtherAuthTip": "You can also transfer it to other users to complete the authentication.", "unPermissionRemind.giveOtherAuthBtn": "Transfer to others for authentication >>", "unPermissionRemind.noAuthPaperSign": "If you cannot complete the enterprise certification, you can choose to download and print it for paper signature", "unPermissionRemind.otherPaperSign": "You can also choose to download the contract document and print, signed by the corporate seal head line seal.", "unPermissionRemind.paperSignBtn": "Signing on paper >>", "unPermissionRemind.applyBtn": "Apply for permission", "unPermissionRemind.authBtn": "Authentication", "unPermissionRemind.returnBtn": "Return", "unPermissionRemind.transferContract": "Transfer to others", "unPermissionRemind.cantTransferTip": "收件方企业存在多业务线，暂不支持移交", "applyPermission.transferAdmin": "Transfer to the master administrator", "applyPermission.transferAdminTip": "The following permissions will be reserved for you after the transfer to the master administrator, and you can also choose additional permissions.", "applyPermission.giveOtherAuth": "Transfer to others for authentication", "applyPermission.giveOtherAuthTip": "During the process of transferring authentication to another person, we are also applying for the following permissions on your behalf to facilitate smooth contract signing. You can also choose additional permissions based on your actual needs.", "applyPermission.rightApply": "Permission request", "applyPermission.adminName": "Name of the system administrator", "applyPermission.account": "Account", "applyPermission.applyUserName": "Applicant's name", "applyPermission.senderNamePlaceholder": "Please enter your name", "applyPermission.senderAccountPlaceholder": "Please enter your account number.", "applyPermission.specialSeal": "Special seal for the template", "applyPermission.electronicSeal": "Electronic seal", "applyPermission.phoneOrEmail": "Cell phone/email", "applyPermission.applyRight": "Request permission", "applyPermission.ent": "Company", "applyPermission.receiverAccountPlaceholder": "Please enter the account number of the transferee", "applyPermission.sender": "Transferor", "applyPermission.yourName": "Your name", "applyPermission.applyName": "Applicant's name", "applyPermission.moreRight": "More permissions", "applyPermission.confirmAdminTip": "I am the administrator. I need to", "applyPermission.confirmAdminBtn": "Request to be the main administration", "applyPermission.otherWayApply": "Request by other means", "applyPermission.applyToAdmin": "Request to the master administrator", "applyPermission.sendBtn": "Deliver notice", "applyPermission.applyRightTip": "If you do not know the master administrator, you can choose other ways to request and get permission from other managers or seal managers of the company to view the contract.", "applyPermissionResult.title": "Notification successful, awaiting processing", "applyPermissionResult.content11": "You will receive text message notification after review by the master administrator ({name}，{account})", "applyPermissionResult.content12": "You will receive text message notification after review by the master administrator ({account})", "applyPermissionResult.content2": "After the transferee {account} receives the notification, your transfer request will be processed. We will inform you of the processing result via text messages.", "applyPermissionResult.returnBtn": "Return", "applyPermissionResult.bindingError": "账号暂未单点绑定成功，无法跳转，请等待审批完成", "morePermissionDialog.title": "More permissions", "morePermissionDialog.confirmBtn": "Confirm", "morePermissionDialog.cancel": "Cancel", "morePermissionDialog.viewContract": "View Company Contracts", "morePermissionDialog.nowContract": "Current contract", "morePermissionDialog.nowSenderProxyContract": "Contracts from the current sender", "morePermissionDialog.allSenderProxyContract": "All contracts", "morePermissionDialog.proxyContractTip": "Contracts requesting viewing permissions include pending contracts without specified signer accounts or contracts where other accounts claim the designated signing account.", "morePermissionDialog.signContract": "Sign corporate contracts (electronic seals)", "morePermissionDialog.nowSenderContract": "Contracts Sent by Current Sender", "morePermissionDialog.allSenderContract": "All contracts", "morePermissionDialog.noApplySignRight": "I am not signing, so I don't need the seal.", "morePermissionDialog.canSignTip": "Signatures are allowed within the viewable scope.", "morePermissionDialog.allTip": "Note: The current sender consists of the company as well as its parent company, subsidiaries, and business divisions.", "morePermissionDialog.error.1": "请输入被转交人账号", "morePermissionDialog.error.2": "请输入正确的账号！", "morePermissionDialog.error.3": "Enter applicant's full name", "morePermissionDialog.error.4": "请您先向主管理员申请权限，若主管理员未响应，可以使用“其他方式申请”的通道", "applyToBeAdminDialog.title": "Appeal to become the master administrator.", "applyToBeAdminDialog.content1": "You are applying to become the system master administrator of {entName} company. The main duties and privileges of the master administrator are:", "applyToBeAdminDialog.content2": "1. Corporate seal use and distribution | 2. Corporate member management | 3. Corporate contract management", "applyToBeAdminDialog.tip": "More master administrator functions can be found on http://ent.bestsign.cn using PC after successful application.", "applyToBeAdminDialog.footer": "The system administrator role is typically held by the company's legal representative, financial manager, legal affairs manager, IT department manager, or business operations manager to ensure effective fulfillment of responsibilities.", "applyToBeAdminDialog.confirm": "Apply", "applyToBeAdminDialog.cancel": "Cancel", "infoProtectDialog.userAuth": "使用刷脸服务须同意", "infoProtectDialog.titleWithSeperator": "《上上签如何保护您的个人信息》", "infoProtectDialog.title": "上上签如何保护您的个人信息", "infoProtectDialog.auth": "实名认证", "infoProtectDialog.faceSign": "刷脸签署", "infoProtectDialog.contentDesp": "您提交个人身份等信息（以下简称\"个人信息\"）时已经充分知悉并同意：", "infoProtectDialog.detailTip1": "（1）为了完成您的{title}，您已经授权您的企业自行或委托上上签将您的个人信息提交给为实现电子签约之目的而提供相应服务的其他主体（比如CA机构、公证处等）；", "infoProtectDialog.detailTip2": "In addition to (1) the authorization content, you separately agree to submit your facial information for willingness verification (i.e., facial recognition signature verification) for this {title}, and agree that BestSign may process your facial information through review, storage, retrieval, sharing and other methods solely for the purpose of providing electronic signature services and subsequent proof issuance. If you do not agree with the content described in this clause, you should immediately stop submitting your facial information and choose other verification methods.", "infoProtectDialog.detailTip3": "（3）除了前述（1）（2）以及法律规定的情形外，上上签未经您的授权不会主动将您的个人信息提交给任何第三人。", "infoProtectDialog.know": "知道了", "faceSign.faceFailed": "非常抱歉，您的人脸比对失败", "faceSign.verifyTry": "请核实身份信息后重试", "faceSign.upSignReq": "今天的人脸比对次数已达到上限，请明天重试或联系合同发起者修改签署要求", "faceSign.reqFace": "发件人要求你进行刷脸校验", "faceSign.signAfterFace": "刷脸通过后即可完成合同签署", "faceSign.qrcodeInvalid": "二维码信息已过期，请刷新", "faceSign.nameIs": "姓名为", "faceSign.IDNumIs": "身份证号为", "faceSign.retry": "重试", "faceSign.pleaseScanToSign": "Please use Alipay/WeChat app to scan the QR code to sign.", "faceSign.pleaseScanAliPay": "请使用支付宝app扫描二维码签署", "faceSign.pleaseScanWeChat": "请使用微信app扫描二维码签署", "entDocList.title": "Corporate Contract Fulfillment Management", "entDocList.goBackDocList": "Go back to My Contract Management", "entDocList.createEntFolder": "Create a contract fulfillment folder", "entDocList.columnFolderName": "Name of the contract fulfillment folder", "entDocList.columnCreateUserName": "Created by", "entDocList.columnRemark": "Remarks", "entDocList.columnPermission": "Permissions", "entDocList.permissionManage": "Permissions management", "entDocList.emptyText": "No data available at this time", "entDocList.operate": "Operation", "entDocList.view": "View", "entDocList.noPermission": "You do not have permission to view the list details", "entDocList.delete": "删除", "entDocList.tip": "提示", "entDocList.deleteTip": "删除后，文件夹中的履约材料以及各项配置将消失。是否确定删除此文件夹？", "entDocList.deleteSuccess": "删除成功", "entDocList.confirm": "确定", "entDocList.cancel": "取消", "entFolderPermission.title": "Corporate Contract Fulfillment Management", "entFolderPermission.myPermission": "Folder permissions for the current account contain:", "entFolderPermission.assignPermission": "Assign folder permissions", "entFolderPermission.unAssignPermission": "Take back permissions", "entFolderPermission.permissionResult": "Authorization results", "entFolderPermission.permissionLog": "Authorization log", "entFolderAssignPermission.title": "Assign folder permissions | Take back folder permissions", "entFolderAssignPermission.next": "Next", "entFolderAssignPermission.cancel": "Cancel", "entFolderPermissionPerson.title": "Select person for authorization | taking back authorization", "entFolderPermissionPerson.role": "Authorized role", "entFolderPermissionPerson.user": "Authorized person", "entFolderPermissionPerson.confirm": "Confirm", "entFolderEditFolder.title.folderName": "Rename the folder", "entFolderEditFolder.title.remark": "Folder notes", "entFolderEditFolder.folderNamePlaceholder": "Please enter a folder name", "entFolderEditFolder.folderNameEmpty": "The name of the contract fulfillment folder cannot be empty!", "entFolderEditFolder.remark": "Fill in the remark", "entFolderEditFolder.remarkPlaceholder": "Please enter", "entFolderEditFolder.confirm": "Confirm", "entFolderEditFolder.cancel": "Cancel", "transferFolder.title": "Transfer of the contract fulfillment folder", "transferFolder.oldOwner": "Previous holder:", "transferFolder.newOwner": "New holder:", "transferFolder.confirm": "Confirm", "transferFolder.successTip": "Transfer successful", "entDoLisSlider.back": "Return to My Contract Management", "entDoLisSlider.reName": "<PERSON><PERSON>", "entDoLisSlider.transfer": "Transfer", "entDoLisSlider.editPermission": "Edit permissions", "entDoLisSlider.editReMark": "Remarks", "entDoLisSlider.remind": "Compliance Reminder", "entDoLisSlider.creator": "Creator:", "entDoLisSlider.myPermission": "My permissions:", "entDoLisSlider.remark": "Note:", "entDoLisSlider.borrowApprover": "Reviewer for borrowing to read:", "entDoLisSlider.addApprover": "+ newly added", "entDoLisSlider.deleteApproverSuc": "Deleted successfully", "entDoLisSlider.know": "Got it.", "entDoLisSlider.deleteApproverTip": "If a corporate folder does not have a reviewer for borrowing to read, then \"borrowing to read privileges\" will not be available, i.e. users will not be able to borrow the contracts in this folder. So the last reviewer for borrowing to read will be kept.", "entDoLisSlider.deleteApproverTitle": "Tips", "entDocTable.operate": "Operate", "entDocTable.download": "Download", "entDocTable.view": "View", "entDocTable.borrow": "Review of \"borrowing to read\"", "entSearch.moreBusinessFields": "More (business fields)", "entSearch.plsSelect": "Please select", "entSearch.contractInvolveTip": "Contracts involving individual or corporate members", "entSearch.searchFor": "Search", "entSearch.unfold": "Expand", "entSearch.putAway": "Collapse", "entSearch.export": "Export folder details", "entSearch.listConfig": "Configure folder details", "entSearch.plsInput": "Please enter", "entSearch.inputSenderPersonAndCompany": "Please fill in individual/company name", "entSearch.inputReceiverPersonAndCompany": "Please fill in individual/company name", "entSearch.plsSelectData": "Please select dates", "entSearch.confirm": "Confirm", "entSearch.noPermission": "You do not have permission to view the list details", "entSearch.notSupported": "This issue is temporarily unsupported", "entDocDetail.mainContractDetail": "Master contract details", "entDocDetail.correctorContract": "Associated contracts", "entDocDetail.contractInfo": "Contract information.", "entDocDetail.contractTitle": "Contract title.", "entDocDetail.contractId": "Contract number.", "entDocDetail.idMainContract": "Is the contract a master contract:", "entDocDetail.entFolderName": "Name of the contract fulfillment folder:", "entDocDetail.tag": "Contract fulfillment label:", "entDocDetail.loanRecord.title": "Borrowing to read record", "entDocDetail.loanRecord.account": "Borrowing to read account number", "entDocDetail.loanRecord.time": "Borrowing to read time", "entDocDetail.loanRecord.duration": "Borrowing to read period", "entDocDetail.loanRecord.timeUnit": "day", "entDocDetail.fulfillMaterial.title": "Fulfill material", "entDocDetail.fulfillMaterial.download": "Download", "entDocDetail.fulfillMaterial.rename": "<PERSON><PERSON>", "entDocDetail.fulfillMaterial.delete": "Delete", "entDocDetail.fulfillMaterial.upload": "Upload", "entDocDetail.fulfillMaterial.fileRename": "File rename", "entDocDetail.fulfillMaterial.fileName": "Filename", "entDocDetail.fulfillMaterial.pleaseEnter": "Please enter", "entDocDetail.fulfillMaterial.cancel": "Cancel", "entDocDetail.fulfillMaterial.confirm": "Confirm", "entDocDetail.fulfillMaterial.fileLimitTip": "单文件不超过16M", "entDocDetail.fulfillMaterial.emptyFileNameTip": "文件名不能为空", "noReadPermissionTip.title": "Tips", "noReadPermissionTip.tip": "You do not have permission to view contracts and borrow contracts to read in the current corporate folder. Please contact your administrator to obtain the relevant permission and try again.", "noReadPermissionTip.know": "Got it.", "applyBorrowDialog.title": "Apply for borrowing to read the contract", "applyBorrowDialog.borrowTime": "Borrowing to read period:", "applyBorrowDialog.timePlaceHolder": "Please select", "applyBorrowDialog.reasonPlaceHolder": "Please fill in the reason for borrowing, so that the approver can decide whether to allow. Optional, no more than 255 characters.", "applyBorrowDialog.approver": "Reviewer", "applyBorrowDialog.rejectReason": "Reason for rejection", "applyBorrowDialog.cancel": "Cancel", "applyBorrowDialog.reApply": "Re-apply", "applyBorrowDialog.confirm": "Confirm", "applyBorrowDialog.applyingStatus": "Under review", "applyBorrowDialog.successStatus": "Review passed", "applyBorrowDialog.rejectedStatus": "Failed to pass review", "applyBorrowDialog.noTimesTip": "Please select the borrowing period first!", "applyBorrowDialog.applySendTip": "Application sent successfully!", "addApproverDialog.title": "Select personnel", "addApproverDialog.confirm": "Confirm", "addApproverDialog.maxApproverTip": "No more than 3 approvers! ", "addApproverDialog.noApproverTip": "Please select the person first!", "borrowApproval.title": "Approval for borrowing to read", "borrowApproval.applicant": "Borrowers", "borrowApproval.borrowTime": "Borrowing period", "borrowApproval.borrowReason": "Reason for borrowing to read", "borrowApproval.approvalRes": "Borrowing to read result", "borrowApproval.reject": "Rejected", "borrowApproval.agree": "Pass", "borrowApproval.rejectTitle": "Reason for rejection", "borrowApproval.rejectPlaceHolder": "Please fill in the reason for rejection. Optional, no more than 255 characters", "borrowApproval.cancel": "Cancel", "borrowApproval.confirm": "Confirm", "components.sendPointPosition.index-1c6dcb-1": "There are stamps/signatures that didn't match the keywords and are temporarily placed in the lower left corner of the first page of the contract, requiring you to manually adjust them one by one.", "components.sendPointPosition.index-1c6dcb-2": "Tips", "components.sendPointPosition.index-1c6dcb-3": "OK", "components.sendPointPosition.index-1c6dcb-4": "Document：", "operate.certificateReport": "Electronic signing and certification report", "operate.notarization": "Notarization", "operate.selectProject": "Please select application", "operate.downloadAttacment": "Download E-Signing Certificate", "operate.downloadAttacmentTip": "The Esign E-Signing Certificate documents the contract's basic information and provides a complete record of all signers and signing activities throughout the contract lifecycle.", "special-doc-dialog.tip1": "The system detected that the document was rotated clockwise [{rotateContent}] and required preprocessing before the contract could be initiated. The entire process has been completed automatically by the system.", "special-doc-dialog.tip2": "Common reasons for preprocessing", "special-doc-dialog.tip3": "When viewing the file in Adobe Reader (including other PDF readers, such as Foxit Reader, etc.), it was found that the layout was flipped, and the file was \"rotated\".", "special-doc-dialog.tip4": "For example: a paper document that should be in horizontal format is processed into vertical format when scanned using a scanner. When reading the PDF scan using Adobe, it is found that the text on the page is inverted, so click the rotate button on the Adobe Reader toolbar to \"rotate\" the PDF of this page back to horizontal format.", "special-doc-dialog.tip5": "Effect after preprocessing (file size will increase)", "special-doc-dialog.tip6": "1. Any CA certificates or other encryption strategies added to the original document will become invalid after preprocessing.", "special-doc-dialog.tip7": "2. The text on the sent contract will not be selectable or copyable.", "special-doc-dialog.tip8": "3. The document clarity may decrease and the file size may increase", "special-doc-dialog.tip9": "If you want to continue using the pre-processed file, please click the \"Continue\" button to complete the contract sending process", "special-doc-dialog.tip10": "Directly use the original file and skip system preprocessing", "special-doc-dialog.tip11": "Method 1: Use Adobe Reader (including other PDF readers, such as Foxit Reader, etc.) to open the PDF and find the rotated page number. Right-click the page thumbnail to perform the \"Rotate\" operation or click the Rotate button on the Adobe Reader toolbar. Rotate counterclockwise to the original state and re-upload (ignore the file if it is flipped or the text is inverted).", "special-doc-dialog.tip12": "View Schematic", "special-doc-dialog.tip13": "Method 2 (PDF scans are available): Rescan the paper document to get the correct format (the paper is correctly oriented horizontally and vertically when scanning), then re-upload the file and send the contract.", "sealSelectDialog.title1": "Apply for using the seals", "sealSelectDialog.title2": "Choose the person to stamp on the contract", "sealSelectDialog.nowApplySealList": "You are requesting the following seal", "sealSelectDialog.chooseApplyPersonToDeal": "Please select the person to stamp on the contract and the contract will be handled by the selected person(You can still continue to review and follow up on this contract)", "sealSelectDialog.contactGroupAdminToDistributeSeal": "Please contact the group administrator to assign a seal", "sealSelectDialog.cancel": "Cancel", "sealSelectDialog.confirm": "Confirm", "sealSelectDialog.confirmSucTip": "Application Successful, Awaiting the Other Party’s Processing", "SsoConfirm.index-c220bb-1": "", "SsoConfirm.index-c220bb-2": "", "SsoConfirm.index-c220bb-3": "", "SsoConfirm.index-c220bb-4": "", "SsoConfirm.index-c220bb-5": "", "SsoConfirm.index-c220bb-6": "", "SsoConfirm.index-c220bb-7": "", "SsoConfirm.index-c220bb-8": "", "SsoConfirm.index-c220bb-9": "", "SsoConfirm.index-c220bb-10": "", "SsoConfirm.index-c220bb-11": "", "SsoConfirm.index-c220bb-12": "", "SsoConfirm.index-c220bb-13": "", "sealSelectDialog.applySealTip": "This contract is awaiting {person}({account})'s signature. You can proceed in one of two ways:", "sealSelectDialog.tipContent1": "Invite {person}({account}) to sign: ", "sealSelectDialog.tipContent1Desc": "If you want {person}({account}) to sign, invite him/her to log in to the e-signing platform and complete the signature.", "sealSelectDialog.tipContent2": "Sign yourself: ", "sealSelectDialog.tipContent2Desc": "If you wish to sign the contract personally (and you already have a seal), guide {person}({account}) to reject the contract first:", "sealSelectDialog.refuseSignTip1": "Step 1: Log in to the BestSign E-Signature Cloud Platform and locate the current contract.", "sealSelectDialog.refuseSignTip2": "Step 2: Click the \"Reject\" button on the contract details page.", "sealSelectDialog.refuseSignTip3": "Step 3: Confirm the rejection in the pop-up dialog.", "sealSelectDialog.refuseSignAfter": "After rejection:", "sealSelectDialog.refuseSignAfterDesc": "Once {person}({account}) completes the rejection, you'll see a new \"Sign\" button on the contract details page. Click this button to complete the contract signing as normal.", "sealSelectDialog.specialScene": "Handling special situations:", "sealSelectDialog.specialSceneOpt": "f you can't contact {person}({account}), you can request the system administrator to intervene. They can use the \"Contract Transfer\" function to transfer signing authority from {person}({account}) to someone else, who can then follow the steps above.", "templateReceiverConfig.err.needAddSender": "Your company/you are not set as a signing party. After the contract is sent out, you will not participate in the signing process. Do you need to add yourself as a signing party?", "templateReceiverConfig.err.addSender": "Add as signing party", "templateReceiverConfig.err.needSeal": "According to [Seal] signing requirements, set [Seal] signing locations in the following documents", "templateReceiverConfig.err.needSignature": "According to [Signature] signing requirements, set [Signature] signing locations in the following documents", "templateReceiverConfig.err.needSealSignature": "According to [Seal And Signature] signing requirements,[Seal] and [Seal Owner Signature] are both required in the same document.", "templateReceiverConfig.err.needSealTip": "The signatory who needs to stamp has other fields on the document (signature date or fields to be filled by the signatory), but the seal field is missing. You can delete other fields or set a seal placement.", "templateReceiverConfig.err.needSignatureTip": "The signatory who needs to sign has other fields on the document (signature date or fields to be filled by the signatory), but the signature field is missing. You can delete other fields or set a signature placement.", "labelLackTip.everyDocSign": "Signers must set signing locations on each document to view and sign it.", "labelNameConflictTip.hasExistName": "There is already {num} field named \"{name}\" in the document.", "labelNameConflictTip.nameChangeAdvice": "If the content of the \"{name}\" field needs to be completely identical, you can continue to use \"{name}\" as the field name. If the content differs, it is recommended to choose a name that accurately describes the field's content or purpose.", "labelNameConflictTip.pleaseChangeName": "The field name \"{name}\" you attempted to use already exists. To avoid confusion, please choose a different name.", "labelNameConflictTip.inputLabelName": "Please enter a new field name in the input box below.", "labelNameConflictTip.closeTip": "If you close the pop-up window, the field will not be saved and will be automatically deleted.", "labelNameConflictTip.closeTipOfEdit": "If you close the pop-up window, the name will not be saved.", "labelEdit.importantNotice": "Important Notice", "labelEdit.tipInfo": "It is strongly recommended to configure signatories into multiple signing roles, with each role corresponding to a designated stamping location.", "labelEdit.tipInfos.title1": "Typical scenario:", "labelEdit.tipInfos.con1": "By default, the same electronic seal is used for multiple stamping locations of the same \"signing role\" on an electronic contract. Although it's possible to manually change the electronic seal for each stamping location, this may lead to oversights.", "labelEdit.tipInfos.title2": "Recommended practice: ", "labelEdit.tipInfos.con2": "To ensure accuracy, it's advisable to configure the signer as multiple signing roles within the electronic signature system, with each role corresponding to a specific electronic seal. This way, for each signing instance, the signer needs to separately confirm the electronic seal being used, thus reducing confusion.", "labelEdit.tipInfos.title3": "Special case:", "labelEdit.tipInfos.con3": " If all signing locations indeed require the use of the same electronic seal, then maintaining one signing role corresponding to multiple stamping locations is appropriate within the e-signature platform.", "labelEdit.detailedInstructions": "Detailed Instructions", "labelEdit.operationDiagram": "Operation Diagram", "templateDetail.noEditPermission": "You need to obtain the 'Edit Template' permission before downloading the template file", "templatePermission.table.searchPlaceholder": "Enter account to query template permissions", "permissionRight.dialog.rightName": "Permission Name", "permissionRight.dialog.recycleRight": "How to Revoke Permissions", "permissionRight.dialog.moreTip": "More Information", "permissionRight.dialog.recycleByAccount": "Cancel permissions for account {account}", "permissionRight.dialog.recycleByRole": "Remove this account from roles 「{role}」 (requires access to console)", "permissionRight.dialog.sendTip1": "Contracts can be sent to the following enterprises:", "permissionRight.dialog.sendTip2": "Direct authorization from templates: {ents}", "permissionRight.dialog.sendTip3": "Authorization from group management permissions: {ents}", "permissionRight.dialog.grantManage": "When none of the template users has the \"Permission Assignment\" privilege, the system will automatically grant this privilege to the primary administrator, and this privilege cannot be revoked (the \"Permission Assignment\" privilege can only be revoked from the primary administrator when other users also have this privilege).", "permissionRight.dialog.roleAffect": "This role corresponds to the following accounts in {entName}:", "permissionRight.dialog.noRight": "The current account has not been granted any permissions for this template.", "permissionRight.dialog.more": ", etc. ({count} enterprises)", "agent.riskJudgement": "AI Lawyer", "agent.uploadText": "Please upload the documents for risk assessment", "agent.startTips": "Now we can start to judge the risks", "agent.feedback": "Questionnaire", "agent.satisfy": "Satisfied with the analysis results, continue to the next analysis", "agent.dissatisfy": "Not satisfied with the analysis results, re-analyze", "agent.custom": "Please enter custom review rules", "agent.submit": "Submit", "agent.others": "Others", "agent.autoExtract": "Automatic Extraction Until Completion", "agent.autoRisk": "Auto-Analysis Process Running", "agent.aiGenerated": "AI Generated - © BestSign", "agent.extractTitle": "Information Extraction", "agent.riskTitle": "AI Lawyer", "agent.deepInference": "<PERSON> Lawyer (Deep Inference)", "agent.chooseRisk": "Select File for Analysis", "agent.chooseExtract": "Specify Source File for Extraction", "agent.analyzing": "Analyzing", "agent.advice": "Generating Revision Suggestions", "agent.options": "Generating Options", "agent.selectFunc": "Please select the function you wish to use", "agent.inputTips": "Enter Precise Information", "filter.yes": "Yes", "agent.deepThinking": "Deep Thinking", "agent.deepThoughtCompleted": "Deep Thinking Completed", "agent.original": "Original", "agent.revision": "Revision", "agent.diff": "Comparison", "charge.packagePurchaseTitle": "[{title}] Plan Purchase", "charge.payOnce": "特惠限购一次", "charge.payNow": "Buy Now", "charge.amount": "Quantity", "charge.unitPrice": "Each", "charge.limitTime": "Validity Period", "charge.copy": "Copy", "charge.month": "Month", "charge.compareInfo1": "Usage Instructions：", "charge.compareInfo2": "{index}、The available quota for {type} of purchases is accessible to all members of the enterprise. If you only require it for personal use, you may switch to a personal account by changing the login entity in the upper right corner.", "charge.compareInfo3": "{index}、Usage Calculated Based on Number of Uploaded Contract {per}", "charge.codePay": "Please Scan QR Code to Pay", "charge.aliPay": "Alipay Payment", "charge.wxPay": "WeChat Payment", "charge.paySuccess": "Purchase Successful", "charge.payIno": "Activated Features | Purchased For | Payment Amount", "charge.contactUs": "Contact Us | Scan QR Code to Consult Professional Advisor", "copySealDialog.title": "Copy Stamp Field", "copySealDialog.pageDecTop": "Copy stamp fields from Page ", "copySealDialog.pageDecMiddle": "and <PERSON>", "copySealDialog.pageDecBottom": "", "copySealDialog.dec": "Quickly copy the stamp field of current page to other pages where stamp fields are in the same position as the current page. After copying, the positions of stamp fields on each page can be adjusted independently without affecting each other.", "copySealDialog.moreBtn": "More Details", "copySealDialog.moreDec": "The \"Copy Stamp Field\" button will only appear when stamp fields are set in the first half of the contract document (for example, in a 100-page document, stamp fields on pages 1-50 can be copied to other pages with one click).", "copySealDialog.confirm": "OK", "copySealDialog.cancel": "Cancel", "labelEdit.friendlyReminder": "Friendly Reminder:", "labelEdit.decorateTipInfos.con1": "Among the stamp fields created by one-click copy, only the last stamp uses a digital certificate, which is sufficient to ensure the legitimacy, compliance, authenticity, and validity of the entire signing process.", "labelEdit.decorateTipInfos.con2": "If you need to use a stamping with digital certificate at a specific critical location, you can set up a new stamp field through the \"Step 2: Drag Signing Position\" using the \"Stamp\" control. Such stamp fields will include a digital certificate.", "labels.sealCopy": "Copy Stamp Field", "labels.delCopySeal": "Delete current stamp field only", "labels.delAllCopySeal": "Delete all copied stamp fields", "labelEdit.blankTip": "When you send contracts using contract templates, the system will generate signing positions / fields automatically, without requiring additional specifications from you.", "labelEdit.autoPosition": "Option 1: Generate by keywords", "labelEdit.autoPositionTip": "The positions of signing fields in the contract will be generated according to where the keywords appear in the document. If no keywords exist, they will appear in the lower left corner of the first page.We recommend manually dragging the signing positions to adjust the offset (the relative distance between signing fields and keywords).", "labelEdit.allPage": "Option 2: Generate by preset positions", "labelEdit.allPageTip": "The positions of signing fields in the contract will be generated according to the pre-set coordinates in the template.", "labelEdit.staticPosition": "Generate signing fields at the same position on all pages.", "docDetail.dualRequired": "Dual-recording verification required", "aiAgent.title": "Stamp Agent", "aiAgent.description1": "For all documents uploaded locally, the Agent will automatically help you find the stamping positions for each signatory, replacing the tedious manual operation of positioning stamps.", "aiAgent.description2": "*Single document cannot exceed 50 pages", "aiAgent.description3": "*Combined with 'Template Special Seal', 'Auto Stamp' and other functions, it can achieve complete automation of non-standard contract stamping", "aiAgent.configTitle": "Stamp Rule Configuration", "aiAgent.configTip": "Check the rules you need to use and adjust the order of each rule to clarify the priority when rules conflict.", "aiAgent.rule.needOverlappingSeal": "Need overlapping seal", "aiAgent.rule.needOverlappingSealDesc": "*If not checked, overlapping seal stamping positions will not be automatically added", "aiAgent.rule.reciprocalSealing": "Reciprocal sealing: Match signatory's stamping positions based on existing stamping positions", "aiAgent.rule.reciprocalSealingDesc": "*For example, if the contract already has 3 seals from the other party (distributed on different pages), the system will automatically generate 3 corresponding stamping positions for our party at the corresponding positions. Note: This function only takes effect when one party stamps.", "aiAgent.rule.sealOnLastLine": "If there is no clear stamping position in the file, stamp on the last line of text in the file", "aiAgent.rule.sealOnLastLineDesc": "*Not traditional contracts, files without clear stamping position instructions, such as internal documents that need stamping", "aiAgent.rule.reasonableAreaSealing": "Seals should be placed in reasonable areas (such as near the stamping column, company information on the last page of the contract, etc.)", "aiAgent.rule.reasonableAreaSealingDesc": "*May conflict with rules such as reciprocal sealing and stamping on each page. Priority needs to be adjusted to ensure rules that need to be satisfied first", "aiAgent.rule.sealEachPage": "Stamp on each page", "aiAgent.rule.sealEachPageDesc": "*If it is a reconciliation statement or bidding document, each page needs to be stamped at the same position", "aiAgent.adjustmentTitle": "Self-service Optimization", "aiAgent.positionAdjustment": "(1) Stamping position optimization (excluding overlapping seals)", "aiAgent.moveUp": "The stamping position specified by the system needs to move up", "aiAgent.moveLeft": "The stamping position specified by the system needs to move left", "aiAgent.centimeter": "cm", "aiAgent.adjustTip1": "*Negative numbers can be filled in to represent the opposite direction.", "aiAgent.adjustTip2": "*N centimeters is the size after printing. By default, the diameter of a seal is about 4 centimeters, which can be converted accordingly", "aiAgent.adjustTip3": "*Moving stamping positions may overlap with existing stamping positions, please be aware", "aiAgent.contentAdjustment": "(2) Whether to stamp adjustment", "aiAgent.addSealPrefix": "If the content appears in the file", "aiAgent.addSealSuffix": "the position corresponding to this content needs to be stamped", "aiAgent.removeSealPrefix": "If the content appears in the file", "aiAgent.removeSealSuffix": "the position corresponding to this content does not need to be stamped", "aiAgent.adjustTip4": "*After setting here, the rule will become the highest priority. This configuration only takes effect when stamping on one side", "aiAgent.saveConfig": "Save Configuration", "aiAgent.resetConfig": "Reset Configuration", "aiAgent.featureNotAvailable": "Please contact BestSign support staff to enable paid features", "aiAgent.enableSuccess": "Enabled Stamp Agent", "aiAgent.disableSuccess": "Disabled Stamp Agent", "aiAgent.operationFailed": "Operation failed, please try again later", "aiAgent.templateIdRequired": "Template ID cannot be empty", "aiAgent.saveSuccess": "Saved successfully", "aiAgent.saveFailed": "Save failed, please try again later", "aiAgent.loadFailed": "Failed to load configuration, please try again later", "autoStamp.buttonText": "Auto Stamp", "autoStamp.progressTitle": "Stamp Agent is specifying stamping positions for signatories based on {documentNames} content. After stamping is completed, please review the results. If it meets your expectations, please click the 'Send' button to officially send this contract.", "autoStamp.progressText": "Progress {percentage}%", "autoStamp.cancelButton": "Cancel Auto Stamp", "autoStamp.taskComplete": "Auto stamping completed", "autoStamp.taskFailed": "Auto stamping failed, please try again later"}